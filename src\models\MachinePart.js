import { pool } from '../config/database.js';

class MachinePart {
  constructor(data) {
    this.id = data.id;
    this.machine_id = data.machine_id;
    this.part_id = data.part_id;
    this.quantity_needed = data.quantity_needed;
    this.is_critical = data.is_critical;
    this.replacement_interval_hours = data.replacement_interval_hours;
    this.last_replaced_at = data.last_replaced_at;
    this.notes = data.notes;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;

    // Joined data
    this.part_name = data.part_name;
    this.part_number = data.part_number;
    this.part_category = data.part_category;
    this.quantity_in_stock = data.quantity_in_stock;
    this.minimum_stock_level = data.minimum_stock_level;
    this.unit_price = data.unit_price;
    this.machine_name = data.machine_name;
    this.machine_number = data.machine_number;
  }

  // Masina varuosade leidmine
  static async findByMachine(machineId) {
    try {
      const query = `
        SELECT mp.*,
               p.name as part_name,
               p.part_number,
               p.category as part_category,
               p.quantity_in_stock,
               p.minimum_stock_level,
               p.unit_price,
               p.location,
               CASE
                 WHEN p.quantity_in_stock < mp.quantity_needed THEN 'insufficient'
                 WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 'low'
                 ELSE 'sufficient'
               END as stock_status
        FROM machine_parts mp
        JOIN parts p ON mp.part_id = p.id
        WHERE mp.machine_id = ? AND p.is_active = TRUE
        ORDER BY mp.is_critical DESC, p.name ASC
      `;
      const [rows] = await pool.execute(query, [machineId]);
      return rows.map(row => new MachinePart(row));
    } catch (error) {
      console.error('Error finding machine parts:', error);
      throw error;
    }
  }

  // Varuosa masinate leidmine
  static async findByPart(partId) {
    try {
      const query = `
        SELECT mp.*,
               m.name as machine_name,
               m.machine_number,
               m.location as machine_location
        FROM machine_parts mp
        JOIN machines m ON mp.machine_id = m.id
        WHERE mp.part_id = ?
        ORDER BY mp.is_critical DESC, m.name ASC
      `;
      const [rows] = await pool.execute(query, [partId]);
      return rows.map(row => new MachinePart(row));
    } catch (error) {
      console.error('Error finding part machines:', error);
      throw error;
    }
  }

  // Kõigi masina-varuosa seoste leidmine
  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT mp.*,
               p.name as part_name,
               p.part_number,
               p.category as part_category,
               p.quantity_in_stock,
               p.minimum_stock_level,
               m.name as machine_name,
               m.machine_number
        FROM machine_parts mp
        JOIN parts p ON mp.part_id = p.id
        JOIN machines m ON mp.machine_id = m.id
        WHERE p.is_active = TRUE
      `;
      const params = [];

      if (filters.critical_only) {
        query += ' AND mp.is_critical = TRUE';
      }

      if (filters.low_stock_only) {
        query += ' AND p.quantity_in_stock <= p.minimum_stock_level';
      }

      query += ' ORDER BY mp.is_critical DESC, m.name ASC, p.name ASC';

      const [rows] = await pool.execute(query, params);
      return rows.map(row => new MachinePart(row));
    } catch (error) {
      console.error('Error finding all machine parts:', error);
      throw error;
    }
  }

  // Masina-varuosa seose loomine
  static async create(data) {
    try {
      const query = `
        INSERT INTO machine_parts (
          machine_id, part_id, quantity_needed, is_critical,
          replacement_interval_hours, notes
        ) VALUES (?, ?, ?, ?, ?, ?)
      `;

      const params = [
        data.machine_id,
        data.part_id,
        data.quantity_needed || 1,
        data.is_critical || false,
        data.replacement_interval_hours || null,
        data.notes || null
      ];

      const [result] = await pool.execute(query, params);
      return await MachinePart.findById(result.insertId);
    } catch (error) {
      console.error('Error creating machine part:', error);
      throw error;
    }
  }

  // Seose leidmine ID järgi
  static async findById(id) {
    try {
      const query = `
        SELECT mp.*,
               p.name as part_name,
               p.part_number,
               p.category as part_category,
               p.quantity_in_stock,
               p.minimum_stock_level,
               p.unit_price,
               m.name as machine_name,
               m.machine_number
        FROM machine_parts mp
        JOIN parts p ON mp.part_id = p.id
        JOIN machines m ON mp.machine_id = m.id
        WHERE mp.id = ?
      `;
      const [rows] = await pool.execute(query, [id]);
      return rows.length > 0 ? new MachinePart(rows[0]) : null;
    } catch (error) {
      console.error('Error finding machine part by ID:', error);
      throw error;
    }
  }

  // Seose uuendamine
  static async update(id, data) {
    try {
      const query = `
        UPDATE machine_parts SET
          quantity_needed = ?,
          is_critical = ?,
          replacement_interval_hours = ?,
          notes = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      const params = [
        data.quantity_needed || 1,
        data.is_critical || false,
        data.replacement_interval_hours || null,
        data.notes || null,
        id
      ];

      await pool.execute(query, params);
      return await MachinePart.findById(id);
    } catch (error) {
      console.error('Error updating machine part:', error);
      throw error;
    }
  }

  // Viimase vahetamise aja uuendamine
  static async updateLastReplaced(id, replacedAt = null) {
    try {
      const query = `
        UPDATE machine_parts SET
          last_replaced_at = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      const timestamp = replacedAt || new Date();
      await pool.execute(query, [timestamp, id]);
      return await MachinePart.findById(id);
    } catch (error) {
      console.error('Error updating last replaced:', error);
      throw error;
    }
  }

  // Seose kustutamine
  static async delete(id) {
    try {
      const query = 'DELETE FROM machine_parts WHERE id = ?';
      await pool.execute(query, [id]);
      return true;
    } catch (error) {
      console.error('Error deleting machine part:', error);
      throw error;
    }
  }

  // Kriitiliste varuosade leidmine
  static async findCriticalParts() {
    try {
      const query = `
        SELECT mp.*,
               p.name as part_name,
               p.part_number,
               p.quantity_in_stock,
               p.minimum_stock_level,
               m.name as machine_name,
               m.machine_number,
               CASE
                 WHEN p.quantity_in_stock < mp.quantity_needed THEN 'insufficient'
                 WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 'low'
                 ELSE 'sufficient'
               END as stock_status
        FROM machine_parts mp
        JOIN parts p ON mp.part_id = p.id
        JOIN machines m ON mp.machine_id = m.id
        WHERE mp.is_critical = TRUE AND p.is_active = TRUE
        ORDER BY
          CASE
            WHEN p.quantity_in_stock < mp.quantity_needed THEN 1
            WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 2
            ELSE 3
          END,
          m.name ASC, p.name ASC
      `;
      const [rows] = await pool.execute(query);
      return rows.map(row => new MachinePart(row));
    } catch (error) {
      console.error('Error finding critical parts:', error);
      throw error;
    }
  }

  // Vahetamist vajavate osade leidmine (replacement interval põhjal)
  static async findPartsNeedingReplacement() {
    try {
      const query = `
        SELECT mp.*,
               p.name as part_name,
               p.part_number,
               p.quantity_in_stock,
               m.name as machine_name,
               m.machine_number,
               m.total_runtime_hours,
               COALESCE(m.total_runtime_hours, 0) -
               COALESCE(TIMESTAMPDIFF(HOUR, mp.last_replaced_at, NOW()), 0) as hours_since_replacement
        FROM machine_parts mp
        JOIN parts p ON mp.part_id = p.id
        JOIN machines m ON mp.machine_id = m.id
        WHERE mp.replacement_interval_hours IS NOT NULL
          AND p.is_active = TRUE
          AND (
            mp.last_replaced_at IS NULL
            OR TIMESTAMPDIFF(HOUR, mp.last_replaced_at, NOW()) >= mp.replacement_interval_hours
          )
        ORDER BY mp.is_critical DESC,
                 (COALESCE(TIMESTAMPDIFF(HOUR, mp.last_replaced_at, NOW()), 0) - mp.replacement_interval_hours) DESC
      `;
      const [rows] = await pool.execute(query);
      return rows.map(row => new MachinePart(row));
    } catch (error) {
      console.error('Error finding parts needing replacement:', error);
      throw error;
    }
  }

  // Statistika
  static async getStatistics() {
    try {
      const query = `
        SELECT
          COUNT(*) as total_machine_parts,
          COUNT(CASE WHEN mp.is_critical = TRUE THEN 1 END) as critical_parts_count,
          COUNT(CASE WHEN p.quantity_in_stock < mp.quantity_needed THEN 1 END) as insufficient_stock_count,
          COUNT(CASE WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 1 END) as low_stock_count,
          AVG(mp.quantity_needed) as average_quantity_needed
        FROM machine_parts mp
        JOIN parts p ON mp.part_id = p.id
        WHERE p.is_active = TRUE
      `;
      const [rows] = await pool.execute(query);
      return rows[0];
    } catch (error) {
      console.error('Error getting machine parts statistics:', error);
      throw error;
    }
  }
}

export default MachinePart;
