import { Hono } from 'hono';
import { MachineGroup } from '../../models/MachineGroup.js';

export const machineGroupApiRoutes = new Hono();

// Specific routes first (before parameterized routes)

// GET /api/machine-groups/statistics - Get machine group statistics
machineGroupApiRoutes.get('/statistics', async (c) => {
  try {
    const stats = await MachineGroup.getStatistics();
    return c.json(stats);
  } catch (error) {
    console.error('Error fetching machine group statistics:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// GET /api/machine-groups/icons - Get available icons
machineGroupApiRoutes.get('/icons', async (c) => {
  try {
    const icons = await MachineGroup.getAvailableIcons();
    return c.json(icons);
  } catch (error) {
    console.error('Error fetching available icons:', error);
    return c.json({ error: 'Failed to fetch icons' }, 500);
  }
});

// GET /api/machine-groups/colors - Get available colors
machineGroupApiRoutes.get('/colors', async (c) => {
  try {
    const colors = await MachineGroup.getAvailableColors();
    return c.json(colors);
  } catch (error) {
    console.error('Error fetching available colors:', error);
    return c.json({ error: 'Failed to fetch colors' }, 500);
  }
});

// GET /api/machine-groups/:id - Get machine group by ID (must be after specific routes)
machineGroupApiRoutes.get('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    
    // Check if id is actually a number (not a string like 'statistics')
    if (isNaN(id)) {
      return c.json({ error: 'Invalid group ID' }, 400);
    }
    
    const group = await MachineGroup.findById(id);
    
    if (!group) {
      return c.json({ error: 'Machine group not found' }, 404);
    }
    
    return c.json(group);
  } catch (error) {
    console.error('Error fetching machine group:', error);
    return c.json({ error: 'Failed to fetch machine group' }, 500);
  }
});

// GET /api/machine-groups - Get all machine groups with optional filtering
machineGroupApiRoutes.get('/', async (c) => {
  try {
    const query = c.req.query();
    const filters = {};
    
    // Parse query parameters for filtering
    if (query.is_active !== undefined) {
      filters.is_active = query.is_active === 'true';
    }
    
    if (query.search) {
      filters.search = query.search;
    }
    
    if (query.limit) {
      filters.limit = parseInt(query.limit);
    }

    const groups = await MachineGroup.findAll(filters);
    return c.json(groups);
  } catch (error) {
    console.error('Error fetching machine groups:', error);
    return c.json({ error: 'Failed to fetch machine groups' }, 500);
  }
});

// POST /api/machine-groups - Create new machine group
machineGroupApiRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    
    // Validate required fields
    if (!body.name) {
      return c.json({ error: 'Missing required fields: name' }, 400);
    }

    const group = await MachineGroup.create(body);
    return c.json(group, 201);
  } catch (error) {
    console.error('Error creating machine group:', error);
    
    if (error.message.includes('already exists')) {
      return c.json({ error: error.message }, 400);
    }
    
    return c.json({ error: 'Failed to create machine group' }, 500);
  }
});

// PUT /api/machine-groups/:id - Update machine group
machineGroupApiRoutes.put('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    
    const group = await MachineGroup.update(id, body);
    
    if (!group) {
      return c.json({ error: 'Machine group not found' }, 404);
    }
    
    return c.json(group);
  } catch (error) {
    console.error('Error updating machine group:', error);
    
    if (error.message.includes('already exists')) {
      return c.json({ error: error.message }, 400);
    }
    
    return c.json({ error: 'Failed to update machine group' }, 500);
  }
});

// POST /api/machine-groups/:id/activate - Activate machine group
machineGroupApiRoutes.post('/:id/activate', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const group = await MachineGroup.activate(id);
    
    if (!group) {
      return c.json({ error: 'Machine group not found' }, 404);
    }
    
    return c.json({ message: 'Machine group activated successfully', group });
  } catch (error) {
    console.error('Error activating machine group:', error);
    return c.json({ error: 'Failed to activate machine group' }, 500);
  }
});

// POST /api/machine-groups/:id/deactivate - Deactivate machine group
machineGroupApiRoutes.post('/:id/deactivate', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const group = await MachineGroup.deactivate(id);
    
    if (!group) {
      return c.json({ error: 'Machine group not found' }, 404);
    }
    
    return c.json({ message: 'Machine group deactivated successfully', group });
  } catch (error) {
    console.error('Error deactivating machine group:', error);
    return c.json({ error: 'Failed to deactivate machine group' }, 500);
  }
});

// GET /api/machine-groups/:id/machines - Get machines in a group
machineGroupApiRoutes.get('/:id/machines', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const query = c.req.query();
    const filters = {};
    
    if (query.status) {
      filters.status = query.status;
    }
    
    if (query.limit) {
      filters.limit = parseInt(query.limit);
    }

    const machines = await MachineGroup.getGroupMachines(id, filters);
    return c.json(machines);
  } catch (error) {
    console.error('Error fetching group machines:', error);
    return c.json({ error: 'Failed to fetch group machines' }, 500);
  }
});

// DELETE /api/machine-groups/:id - Delete machine group
machineGroupApiRoutes.delete('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await MachineGroup.delete(id);
    
    if (!deleted) {
      return c.json({ error: 'Machine group not found' }, 404);
    }
    
    return c.json({ message: 'Machine group deleted successfully' });
  } catch (error) {
    console.error('Error deleting machine group:', error);
    
    if (error.message.includes('contains machines')) {
      return c.json({ error: error.message }, 400);
    }
    
    return c.json({ error: 'Failed to delete machine group' }, 500);
  }
});
