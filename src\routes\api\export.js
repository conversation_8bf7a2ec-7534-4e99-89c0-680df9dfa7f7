import { Hono } from 'hono';
import { Machine } from '../../models/Machine.js';
import { MachineGroup } from '../../models/MachineGroup.js';
import { Issue } from '../../models/Issue.js';
import { MaintenanceRequest } from '../../models/MaintenanceRequest.js';

const exportRoutes = new Hono();

// Export groups analytics to PDF
exportRoutes.get('/groups/analytics/pdf', async (c) => {
  try {
    const query = c.req.query();
    const startDate = query.start_date || null;
    const endDate = query.end_date || null;
    const groupId = query.group_id ? parseInt(query.group_id) : null;

    // Get analytics data (reuse logic from reports)
    const groups = groupId
      ? [await MachineGroup.findById(groupId)]
      : await MachineGroup.findAll({ is_active: true });

    const analytics = [];
    for (const group of groups) {
      if (!group) continue;

      const machines = await Machine.findAll({ group_id: group.id });
      const machineIds = machines.map(m => m.id);

      const issueFilters = { machine_ids: machineIds };
      if (startDate) issueFilters.start_date = startDate;
      if (endDate) issueFilters.end_date = endDate;

      const issues = await Issue.findAll(issueFilters);
      const issueStats = {
        total: issues.length,
        open: issues.filter(i => i.status === 'open').length,
        in_progress: issues.filter(i => i.status === 'in_progress').length,
        resolved: issues.filter(i => i.status === 'resolved').length,
        critical: issues.filter(i => i.priority === 'critical').length,
        high: issues.filter(i => i.priority === 'high').length
      };

      const maintenanceFilters = { machine_ids: machineIds };
      if (startDate) maintenanceFilters.start_date = startDate;
      if (endDate) maintenanceFilters.end_date = endDate;

      const maintenance = await MaintenanceRequest.findAll(maintenanceFilters);
      const maintenanceStats = {
        total: maintenance.length,
        pending: maintenance.filter(m => m.status === 'pending').length,
        scheduled: maintenance.filter(m => m.status === 'scheduled').length,
        in_progress: maintenance.filter(m => m.status === 'in_progress').length,
        completed: maintenance.filter(m => m.status === 'completed').length,
        urgent: maintenance.filter(m => m.priority === 'urgent').length
      };

      const machineStats = {
        total: machines.length,
        online: machines.filter(m => m.status === 'online').length,
        offline: machines.filter(m => m.status === 'offline').length,
        maintenance: machines.filter(m => m.status === 'maintenance').length
      };

      const totalIssues = issueStats.total;
      const resolvedIssues = issueStats.resolved;
      const resolutionRate = totalIssues > 0 ? (resolvedIssues / totalIssues * 100).toFixed(1) : 0;

      const totalMaintenance = maintenanceStats.total;
      const completedMaintenance = maintenanceStats.completed;
      const maintenanceCompletionRate = totalMaintenance > 0 ? (completedMaintenance / totalMaintenance * 100).toFixed(1) : 0;

      const uptime = machineStats.total > 0 ? (machineStats.online / machineStats.total * 100).toFixed(1) : 0;

      analytics.push({
        group: {
          id: group.id,
          name: group.name,
          description: group.description,
          color: group.color,
          icon: group.icon
        },
        machines: machineStats,
        issues: issueStats,
        maintenance: maintenanceStats,
        metrics: {
          uptime_percentage: parseFloat(uptime),
          issue_resolution_rate: parseFloat(resolutionRate),
          maintenance_completion_rate: parseFloat(maintenanceCompletionRate),
          issues_per_machine: machineStats.total > 0 ? (totalIssues / machineStats.total).toFixed(2) : 0,
          maintenance_per_machine: machineStats.total > 0 ? (totalMaintenance / machineStats.total).toFixed(2) : 0
        }
      });
    }

    // Generate PDF HTML
    const html = generatePDFHTML(analytics, { startDate, endDate, groupId });

    // Check if download is requested
    const download = query.download === 'true';

    if (download) {
      // Set headers for download
      c.header('Content-Type', 'text/html; charset=utf-8');
      c.header('Content-Disposition', `attachment; filename="gruppide-analyytika-${new Date().toISOString().split('T')[0]}.html"`);
    } else {
      // Set headers for inline viewing
      c.header('Content-Type', 'text/html');
      c.header('Content-Disposition', `inline; filename="gruppide-analyytika-${new Date().toISOString().split('T')[0]}.html"`);
    }

    return c.html(html);
  } catch (error) {
    console.error('Error generating PDF export:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Export groups analytics to Excel (CSV format)
exportRoutes.get('/groups/analytics/excel', async (c) => {
  try {
    const query = c.req.query();
    const startDate = query.start_date || null;
    const endDate = query.end_date || null;
    const groupId = query.group_id ? parseInt(query.group_id) : null;

    // Get analytics data (same as PDF)
    const groups = groupId
      ? [await MachineGroup.findById(groupId)]
      : await MachineGroup.findAll({ is_active: true });

    const csvData = [];
    csvData.push([
      'Grupi ID',
      'Grupi nimi',
      'Kirjeldus',
      'Masinate arv',
      'Online masinad',
      'Offline masinad',
      'Hoolduses masinad',
      'Töökindlus %',
      'Rikked kokku',
      'Avatud rikked',
      'Töös rikked',
      'Lahendatud rikked',
      'Kriitilised rikked',
      'Kõrge prioriteet rikked',
      'Rikete lahendamise määr %',
      'Rikked masina kohta',
      'Hooldused kokku',
      'Ootel hooldused',
      'Planeeritud hooldused',
      'Töös hooldused',
      'Lõpetatud hooldused',
      'Kiireloomulised hooldused',
      'Hoolduste lõpetamise määr %',
      'Hooldused masina kohta'
    ]);

    for (const group of groups) {
      if (!group) continue;

      const machines = await Machine.findAll({ group_id: group.id });
      const machineIds = machines.map(m => m.id);

      const issueFilters = { machine_ids: machineIds };
      if (startDate) issueFilters.start_date = startDate;
      if (endDate) issueFilters.end_date = endDate;

      const issues = await Issue.findAll(issueFilters);
      const issueStats = {
        total: issues.length,
        open: issues.filter(i => i.status === 'open').length,
        in_progress: issues.filter(i => i.status === 'in_progress').length,
        resolved: issues.filter(i => i.status === 'resolved').length,
        critical: issues.filter(i => i.priority === 'critical').length,
        high: issues.filter(i => i.priority === 'high').length
      };

      const maintenanceFilters = { machine_ids: machineIds };
      if (startDate) maintenanceFilters.start_date = startDate;
      if (endDate) maintenanceFilters.end_date = endDate;

      const maintenance = await MaintenanceRequest.findAll(maintenanceFilters);
      const maintenanceStats = {
        total: maintenance.length,
        pending: maintenance.filter(m => m.status === 'pending').length,
        scheduled: maintenance.filter(m => m.status === 'scheduled').length,
        in_progress: maintenance.filter(m => m.status === 'in_progress').length,
        completed: maintenance.filter(m => m.status === 'completed').length,
        urgent: maintenance.filter(m => m.priority === 'urgent').length
      };

      const machineStats = {
        total: machines.length,
        online: machines.filter(m => m.status === 'online').length,
        offline: machines.filter(m => m.status === 'offline').length,
        maintenance: machines.filter(m => m.status === 'maintenance').length
      };

      const totalIssues = issueStats.total;
      const resolvedIssues = issueStats.resolved;
      const resolutionRate = totalIssues > 0 ? (resolvedIssues / totalIssues * 100).toFixed(1) : 0;

      const totalMaintenance = maintenanceStats.total;
      const completedMaintenance = maintenanceStats.completed;
      const maintenanceCompletionRate = totalMaintenance > 0 ? (completedMaintenance / totalMaintenance * 100).toFixed(1) : 0;

      const uptime = machineStats.total > 0 ? (machineStats.online / machineStats.total * 100).toFixed(1) : 0;

      csvData.push([
        group.id,
        group.name,
        group.description,
        machineStats.total,
        machineStats.online,
        machineStats.offline,
        machineStats.maintenance,
        uptime,
        issueStats.total,
        issueStats.open,
        issueStats.in_progress,
        issueStats.resolved,
        issueStats.critical,
        issueStats.high,
        resolutionRate,
        machineStats.total > 0 ? (totalIssues / machineStats.total).toFixed(2) : 0,
        maintenanceStats.total,
        maintenanceStats.pending,
        maintenanceStats.scheduled,
        maintenanceStats.in_progress,
        maintenanceStats.completed,
        maintenanceStats.urgent,
        maintenanceCompletionRate,
        machineStats.total > 0 ? (totalMaintenance / machineStats.total).toFixed(2) : 0
      ]);
    }

    // Convert to CSV
    const csv = csvData.map(row =>
      row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    // Set CSV headers
    c.header('Content-Type', 'text/csv; charset=utf-8');
    c.header('Content-Disposition', `attachment; filename="gruppide-analyytika-${new Date().toISOString().split('T')[0]}.csv"`);

    return c.text('\ufeff' + csv); // Add BOM for proper UTF-8 encoding
  } catch (error) {
    console.error('Error generating Excel export:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Generate PDF HTML template
function generatePDFHTML(analytics, filters) {
  const currentDate = new Date().toLocaleDateString('et-EE');
  const periodText = filters.startDate && filters.endDate
    ? `${filters.startDate} - ${filters.endDate}`
    : 'Kõik andmed';

  return `
    <!DOCTYPE html>
    <html lang="et">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>CMMS - Gruppide Analüütika Aruanne</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3B82F6; padding-bottom: 20px; }
        .header h1 { color: #3B82F6; margin: 0; }
        .header p { margin: 5px 0; color: #666; }
        .summary { background: #F8FAFC; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .summary h2 { color: #1F2937; margin-top: 0; }
        .summary-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; }
        .summary-item { text-align: center; }
        .summary-value { font-size: 24px; font-weight: bold; color: #3B82F6; }
        .summary-label { font-size: 14px; color: #666; }
        .group { margin-bottom: 30px; border: 1px solid #E5E7EB; border-radius: 8px; overflow: hidden; }
        .group-header { background: #F9FAFB; padding: 15px; border-bottom: 1px solid #E5E7EB; }
        .group-name { font-size: 18px; font-weight: bold; margin: 0; }
        .group-description { color: #666; margin: 5px 0 0 0; }
        .group-content { padding: 20px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px; }
        .metric { text-align: center; }
        .metric-value { font-size: 20px; font-weight: bold; }
        .metric-label { font-size: 12px; color: #666; margin-top: 5px; }
        .details-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; }
        .detail-section h4 { margin: 0 0 10px 0; color: #374151; }
        .detail-item { display: flex; justify-content: space-between; margin: 5px 0; }
        .footer { margin-top: 40px; text-align: center; color: #666; font-size: 12px; border-top: 1px solid #E5E7EB; padding-top: 20px; }
        @media print {
          body { margin: 0; }
          .group { page-break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>CMMS - Gruppide Analüütika Aruanne</h1>
        <p>Genereeritud: ${currentDate}</p>
        <p>Periood: ${periodText}</p>
      </div>

      <div class="summary">
        <h2>📊 Ülevaade</h2>
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-value">${analytics.length}</div>
            <div class="summary-label">Gruppi</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">${analytics.reduce((sum, a) => sum + a.machines.total, 0)}</div>
            <div class="summary-label">Masinat</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">${analytics.reduce((sum, a) => sum + a.issues.total, 0)}</div>
            <div class="summary-label">Rikked</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">${analytics.length > 0 ? (analytics.reduce((sum, a) => sum + a.metrics.uptime_percentage, 0) / analytics.length).toFixed(1) : 0}%</div>
            <div class="summary-label">Keskmine töökindlus</div>
          </div>
        </div>
      </div>

      ${analytics.map(group => `
        <div class="group">
          <div class="group-header">
            <h3 class="group-name">${group.group.name}</h3>
            <p class="group-description">${group.group.description}</p>
          </div>
          <div class="group-content">
            <div class="metrics-grid">
              <div class="metric">
                <div class="metric-value" style="color: #10B981;">${group.metrics.uptime_percentage}%</div>
                <div class="metric-label">Töökindlus</div>
              </div>
              <div class="metric">
                <div class="metric-value" style="color: #3B82F6;">${group.metrics.issue_resolution_rate}%</div>
                <div class="metric-label">Rikete lahendamise määr</div>
              </div>
              <div class="metric">
                <div class="metric-value" style="color: #8B5CF6;">${group.metrics.maintenance_completion_rate}%</div>
                <div class="metric-label">Hoolduste lõpetamise määr</div>
              </div>
            </div>

            <div class="details-grid">
              <div class="detail-section">
                <h4>🔧 Masinad</h4>
                <div class="detail-item"><span>Kokku:</span><span>${group.machines.total}</span></div>
                <div class="detail-item"><span>Online:</span><span>${group.machines.online}</span></div>
                <div class="detail-item"><span>Offline:</span><span>${group.machines.offline}</span></div>
                <div class="detail-item"><span>Hoolduses:</span><span>${group.machines.maintenance}</span></div>
              </div>

              <div class="detail-section">
                <h4>⚠️ Rikked</h4>
                <div class="detail-item"><span>Kokku:</span><span>${group.issues.total}</span></div>
                <div class="detail-item"><span>Avatud:</span><span>${group.issues.open}</span></div>
                <div class="detail-item"><span>Töös:</span><span>${group.issues.in_progress}</span></div>
                <div class="detail-item"><span>Lahendatud:</span><span>${group.issues.resolved}</span></div>
                <div class="detail-item"><span>Kriitilised:</span><span>${group.issues.critical}</span></div>
              </div>

              <div class="detail-section">
                <h4>🔨 Hooldused</h4>
                <div class="detail-item"><span>Kokku:</span><span>${group.maintenance.total}</span></div>
                <div class="detail-item"><span>Ootel:</span><span>${group.maintenance.pending}</span></div>
                <div class="detail-item"><span>Planeeritud:</span><span>${group.maintenance.scheduled}</span></div>
                <div class="detail-item"><span>Töös:</span><span>${group.maintenance.in_progress}</span></div>
                <div class="detail-item"><span>Lõpetatud:</span><span>${group.maintenance.completed}</span></div>
              </div>
            </div>
          </div>
        </div>
      `).join('')}

      <div class="footer">
        <p>CMMS - Computerized Maintenance Management System</p>
        <p>Aruanne genereeritud automaatselt süsteemi poolt</p>
      </div>
    </body>
    </html>
  `;
}

// Export machine performance to Excel (CSV format)
exportRoutes.get('/machines/performance/excel', async (c) => {
  try {
    const query = c.req.query();
    const startDate = query.start_date || null;
    const endDate = query.end_date || null;
    const groupId = query.group_id ? parseInt(query.group_id) : null;

    // Get machines with optional group filter
    const machineFilters = {};
    if (groupId) machineFilters.group_id = groupId;

    const machines = await Machine.findAll(machineFilters);

    const csvData = [];
    csvData.push([
      'Masina ID',
      'Masina number',
      'Masina nimi',
      'Staatus',
      'Grupi nimi',
      'Rikked kokku',
      'Kriitilised rikked',
      'Lahendatud rikked',
      'Rikete lahendamise määr %',
      'Hooldused kokku',
      'Lõpetatud hooldused',
      'Kiireloomulised hooldused',
      'Hoolduste lõpetamise määr %',
      'Töökindluse skoor',
      'Hoolduse skoor',
      'Üldine skoor'
    ]);

    for (const machine of machines) {
      // Get issues for this machine
      const issueFilters = { machine_id: machine.id };
      if (startDate) issueFilters.start_date = startDate;
      if (endDate) issueFilters.end_date = endDate;

      const issues = await Issue.findAll(issueFilters);

      // Get maintenance for this machine
      const maintenanceFilters = { machine_id: machine.id };
      if (startDate) maintenanceFilters.start_date = startDate;
      if (endDate) maintenanceFilters.end_date = endDate;

      const maintenance = await MaintenanceRequest.findAll(maintenanceFilters);

      // Calculate performance metrics
      const totalIssues = issues.length;
      const criticalIssues = issues.filter(i => i.priority === 'critical').length;
      const resolvedIssues = issues.filter(i => i.status === 'resolved').length;

      const totalMaintenance = maintenance.length;
      const completedMaintenance = maintenance.filter(m => m.status === 'completed').length;
      const urgentMaintenance = maintenance.filter(m => m.priority === 'urgent').length;

      // Calculate scores (0-100)
      const reliabilityScore = totalIssues === 0 ? 100 : Math.max(0, 100 - (criticalIssues * 20) - (totalIssues * 5));
      const maintenanceScore = totalMaintenance === 0 ? 100 : (completedMaintenance / totalMaintenance * 100);
      const overallScore = (reliabilityScore + maintenanceScore) / 2;

      const resolutionRate = totalIssues > 0 ? (resolvedIssues / totalIssues * 100).toFixed(1) : 0;
      const completionRate = totalMaintenance > 0 ? (completedMaintenance / totalMaintenance * 100).toFixed(1) : 0;

      csvData.push([
        machine.id,
        machine.machine_number,
        machine.name,
        machine.status,
        machine.group_name || '-',
        totalIssues,
        criticalIssues,
        resolvedIssues,
        resolutionRate,
        totalMaintenance,
        completedMaintenance,
        urgentMaintenance,
        completionRate,
        reliabilityScore.toFixed(1),
        maintenanceScore.toFixed(1),
        overallScore.toFixed(1)
      ]);
    }

    // Convert to CSV
    const csv = csvData.map(row =>
      row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    // Set CSV headers
    c.header('Content-Type', 'text/csv; charset=utf-8');
    c.header('Content-Disposition', `attachment; filename="masinate-joudlus-${new Date().toISOString().split('T')[0]}.csv"`);

    return c.text('\ufeff' + csv); // Add BOM for proper UTF-8 encoding
  } catch (error) {
    console.error('Error generating machine performance Excel export:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

export { exportRoutes };
