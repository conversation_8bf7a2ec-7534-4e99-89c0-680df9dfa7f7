import { pool } from '../config/database.js';

export class MaintenanceRequest {
  static async create(requestData) {
    try {
      const {
        machine_id,
        operator_number,
        operator_name = null,
        maintenance_type = 'preventive',
        urgency = 'medium',
        title,
        description = null,
        requested_date = null
      } = requestData;

      const [result] = await pool.execute(
        `INSERT INTO maintenance_requests (
          machine_id, operator_number, operator_name,
          maintenance_type, urgency, title, description, requested_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [machine_id, operator_number, operator_name, maintenance_type, urgency, title, description, requested_date]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create maintenance request: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          mr.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location,
          mp.company_name as partner_company,
          mp.contact_person as partner_contact,
          mp.email as partner_email,
          mp.phone as partner_phone
         FROM maintenance_requests mr
         JOIN machines m ON mr.machine_id = m.id
         LEFT JOIN maintenance_partners mp ON mr.partner_id = mp.id
         WHERE mr.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find maintenance request: ${error.message}`);
    }
  }

  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT
          mr.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location,
          mp.company_name as partner_company,
          mp.contact_person as partner_contact,
          mp.email as partner_email,
          mp.phone as partner_phone
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        LEFT JOIN maintenance_partners mp ON mr.partner_id = mp.id
      `;

      const conditions = [];
      const params = [];

      if (filters.machine_id) {
        conditions.push('mr.machine_id = ?');
        params.push(filters.machine_id);
      }

      if (filters.status) {
        conditions.push('mr.status = ?');
        params.push(filters.status);
      }

      if (filters.urgency) {
        conditions.push('mr.urgency = ?');
        params.push(filters.urgency);
      }

      if (filters.maintenance_type) {
        conditions.push('mr.maintenance_type = ?');
        params.push(filters.maintenance_type);
      }

      if (filters.scheduled_date_from) {
        conditions.push('mr.scheduled_date >= ?');
        params.push(filters.scheduled_date_from);
      }

      if (filters.scheduled_date_to) {
        conditions.push('mr.scheduled_date <= ?');
        params.push(filters.scheduled_date_to);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' ORDER BY mr.created_at DESC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to fetch maintenance requests: ${error.message}`);
    }
  }

  static async findByMachineId(machineId) {
    try {
      return await this.findAll({ machine_id: machineId });
    } catch (error) {
      throw new Error(`Failed to find maintenance requests for machine: ${error.message}`);
    }
  }

  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      // Build dynamic update query
      for (const [key, value] of Object.entries(updateData)) {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          params.push(value);
        }
      }

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      params.push(id);

      await pool.execute(
        `UPDATE maintenance_requests SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        params
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update maintenance request: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.execute(
        'DELETE FROM maintenance_requests WHERE id = ?',
        [id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete maintenance request: ${error.message}`);
    }
  }

  static async getStatistics() {
    try {
      const [rows] = await pool.execute(`
        SELECT
          COUNT(*) as total_requests,
          SUM(CASE WHEN status = 'requested' THEN 1 ELSE 0 END) as pending_requests,
          SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_requests,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_requests,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_requests,
          SUM(CASE WHEN urgency = 'urgent' THEN 1 ELSE 0 END) as urgent_requests,
          SUM(CASE WHEN urgency = 'high' THEN 1 ELSE 0 END) as high_urgency_requests,
          SUM(CASE WHEN maintenance_type = 'emergency' THEN 1 ELSE 0 END) as emergency_requests
        FROM maintenance_requests
      `);

      return rows[0];
    } catch (error) {
      throw new Error(`Failed to get maintenance request statistics: ${error.message}`);
    }
  }

  static async getUpcomingMaintenance(days = 30) {
    try {
      const [rows] = await pool.execute(`
        SELECT mr.*, m.machine_number, m.name as machine_name, m.location as machine_location
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        WHERE mr.scheduled_date IS NOT NULL
        AND mr.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
        AND mr.status IN ('scheduled', 'in_progress')
        ORDER BY mr.scheduled_date ASC
      `, [days]);

      return rows;
    } catch (error) {
      throw new Error(`Failed to get upcoming maintenance: ${error.message}`);
    }
  }

  static async getOverdueMaintenance() {
    try {
      const [rows] = await pool.execute(`
        SELECT mr.*, m.machine_number, m.name as machine_name, m.location as machine_location
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        WHERE mr.scheduled_date IS NOT NULL
        AND mr.scheduled_date < CURDATE()
        AND mr.status IN ('scheduled', 'in_progress')
        ORDER BY mr.scheduled_date ASC
      `);

      return rows;
    } catch (error) {
      throw new Error(`Failed to get overdue maintenance: ${error.message}`);
    }
  }
}
