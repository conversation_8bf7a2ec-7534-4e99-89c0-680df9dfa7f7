import { readFileSync } from 'fs';
import { pool } from '../src/config/database.js';

async function migrate() {
  try {
    console.log('🔄 Running database migrations...');

    const schema = readFileSync('./database/schema.sql', 'utf8');
    const statements = schema.split(';').filter(stmt => stmt.trim());

    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
      }
    }

    console.log('✅ Database migrations completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

migrate();
