# CMMS (Computerized Maintenance Management System) Arenduse Instruktsioonid

## Projekti Ülevaade

Palun arendada lihtne CMMS tarkvara, mis võimaldab hallata kuni 30 masinat, nende hooldusi, dokumentatsiooni ja varuosasid. Rakendus peab töötama lokaalses võrgus (localhost) ja olema kättesaadav nii arvutist kui mobiilseadmetest.

## Põhifunktsioonid

### 1. Masinate Haldus
- Masinate register koos kõigi andmetega
- Iga masina jaoks genereeritatav QR-kood
- Masina detailvaade hooldusa<PERSON>, dokumentide ja varuosadega

### 2. R<PERSON><PERSON> ja Hoolduste Registreerimine
- Operaatori mobiilne vaade QR-koodi skanneerimiseks
- Rikete/hoolduste teatamine personaalnumbri alusel (sisselogimine ei ole vajalik)
- Automaatsed teavitused hooldusmeeskonnale

### 3. <PERSON><PERSON>uste Planeerimine
- Regulaarsete hoolduste kalender
- Hooldusajaloo jälgimine
- Hooldusmeeskonna töötajate määramine

### 4. Varuosa<PERSON>
- Varuosade laoseisu jälgimine
- Automaatsed teavitused miinimumvaru kohta
- Varuosade seostamine konkreetsete masinatega

### 5. Dokumentide Haldus
- Masinate dokumentide (juhendid, sertifikaadid) haldus
- Failide üleslaadimine ja allalaadimine

### 6. Arendusprojektide Haldus
- Masinate täiustamise projektide jälgimine
- Projektide progress ja tähtajad

### 7. Statistika ja Raportid
- Rikete statistika
- Masinate töökindluse analüüs
- Hoolduste efektiivsuse ülevaated

## Tehnilised Nõuded

### Backend
- **Runtime**: Bun (kiire JavaScript runtime)
- **Framework**: Hono või Express.js (koos static file serving)
- **Template Engine**: EJS või Handlebars (server-side rendering)
- **Andmebaas**: MySQL või MariaDB
- **ORM**: Prisma või Drizzle ORM (head Bun toega)
- **Failihaldus**: Bun.file() failide üleslaadimiseks
- **QR-koodid**: qrcode paketi kasutamine

### Frontend (integreeritud)
- **Rendering**: Server-side rendering + Progressive Enhancement
- **Styling**: Tailwind CSS
- **JavaScript**: Vanilla JS või AlpineJS (lihtne interaktiivsus)
- **Mobile support**: Responsive design Tailwind'iga

### Arhitektuur
- Fullstack monolith rakendus
- Server-side rendering koos progressiivse täiustamisega
- Üks server, üks port (8080)
- MySQL/MariaDB andmebaas (localhost:3306)
- Static assets (CSS, JS, pildid) serveeritakse samast serverist

## Andmebaasi Struktuur

### MySQL/MariaDB andmebaas: `cmms_db`

Palun loo andmebaas nimega `cmms_db` ja järgmised tabelid:

#### machines (masinad)
```sql
CREATE TABLE machines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    manufacturer VARCHAR(255),
    model VARCHAR(255),
    serial_number VARCHAR(255),
    manufacturing_year INT,
    department VARCHAR(255),
    responsible_operator VARCHAR(255),
    location VARCHAR(255),
    status ENUM('online', 'offline', 'maintenance') DEFAULT 'online',
    last_maintenance DATE,
    next_maintenance DATE,
    warranty_end DATE,
    qr_code_data LONGBLOB,
    qr_code_mime_type VARCHAR(100) DEFAULT 'image/png',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### issues (rikked)
```sql
CREATE TABLE issues (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    operator_number VARCHAR(50) NOT NULL,
    issue_type ENUM('mechanical', 'electrical', 'hydraulic', 'software', 'other') NOT NULL,
    issue_category ENUM('issue', 'maintenance') NOT NULL,
    severity ENUM('low', 'medium', 'high') DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    photo_filename VARCHAR(255),
    photo_data LONGBLOB,
    photo_mime_type VARCHAR(100),
    status ENUM('new', 'in_progress', 'waiting', 'completed', 'cancelled') DEFAULT 'new',
    assigned_to VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);
```

#### maintenance_records (hoolduskirjed)
```sql
CREATE TABLE maintenance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    maintenance_type ENUM('regular', 'emergency', 'preventive') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    performed_by VARCHAR(255),
    scheduled_date DATE,
    completed_date DATE,
    duration_hours DECIMAL(5,2),
    cost DECIMAL(10,2),
    parts_used JSON,
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);
```

#### spare_parts (varuosad)
```sql
CREATE TABLE spare_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_number VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    manufacturer VARCHAR(255),
    category VARCHAR(100),
    location VARCHAR(255),
    quantity_in_stock INT DEFAULT 0,
    minimum_stock INT DEFAULT 0,
    unit_price DECIMAL(10,2),
    supplier VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### machine_parts (masina varuosad)
```sql
CREATE TABLE machine_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    part_id INT NOT NULL,
    quantity_needed INT DEFAULT 1,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    FOREIGN KEY (part_id) REFERENCES spare_parts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_machine_part (machine_id, part_id)
);
```

#### documents (dokumendid)
```sql
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    document_type ENUM('manual', 'certificate', 'drawing', 'warranty', 'other') NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_data LONGBLOB NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);
```

#### development_projects (arendusprojektid)
```sql
CREATE TABLE development_projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    initiator VARCHAR(255),
    start_date DATE,
    end_date DATE,
    status ENUM('active', 'completed', 'cancelled', 'on_hold') DEFAULT 'active',
    progress_percentage INT DEFAULT 0,
    tags JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### project_machines (projekti masinad)
```sql
CREATE TABLE project_machines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    machine_id INT NOT NULL,
    FOREIGN KEY (project_id) REFERENCES development_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_machine (project_id, machine_id)
);
```

#### operators (operaatorid)
```sql
CREATE TABLE operators (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operator_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    department VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Endpoints (Fullstack Routes)

### HTML lehekülgede marsruudid
- `GET /` - ümbersuunamine admin töölauale
- `GET /admin` - administraatori töölaud (HTML)
- `GET /machines` - masinate nimekiri (HTML)
- `GET /machines/:id` - masina detailvaade (HTML)
- `GET /maintenance` - hoolduste haldus (HTML)
- `GET /parts` - varuosade haldus (HTML)
- `GET /projects` - arendusprojektide haldus (HTML)
- `GET /operator/:machineNumber` - operaatori mobiilne vaade (HTML)

### API marsruudid (JSON responses)
#### Masinad
- `GET /api/machines` - kõik masinad (JSON)
- `GET /api/machines/:id` - konkreetne masin (JSON)
- `POST /api/machines` - uue masina lisamine
- `PUT /api/machines/:id` - masina uuendamine
- `DELETE /api/machines/:id` - masina kustutamine

#### Rikked
- `GET /api/issues` - kõik rikked (JSON)
- `GET /api/issues/:id` - konkreetne rike (JSON)
- `POST /api/issues` - uue rikke lisamine (AJAX/fetch)
- `PUT /api/issues/:id` - rikke uuendamine

#### Hooldused
- `GET /api/maintenance` - kõik hooldused (JSON)
- `POST /api/maintenance` - uue hoolduse lisamine

#### Varuosad
- `GET /api/parts` - kõik varuosad (JSON)
- `GET /api/parts/low-stock` - madala varuga osad
- `POST /api/parts` - uue osa lisamine
- `PUT /api/parts/:id` - osa uuendamine

#### Arendusprojektid
- `GET /api/projects` - kõik projektid (JSON)
- `POST /api/projects` - uue projekti lisamine
- `PUT /api/projects/:id` - projekti uuendamine

#### Failide haldus
- `GET /api/files/photo/:issueId` - rikke foto allalaadimine  
- `GET /api/files/qr/:machineId` - masina QR-koodi allalaadimine
- `GET /api/files/document/:documentId` - dokumendi allalaadimine
- `POST /api/files/upload` - failide üleslaadimine (multipart/form-data)

#### Statistika
- `GET /api/stats/dashboard` - tööllaua statistika (JSON)
- `GET /api/stats/issues` - rikete statistika (JSON)

## Kasutajaliidese Nõuded

### 1. Operaatori Mobiilne Vaade (`/operator/:machineNumber`)
- **Juurdepääs**: QR-koodi skaneerimine viib sellele lehele
- **Sisaldab**:
  - Masina info (eeltäidetud)
  - Operaatori numbri sisestamise väli
  - Valiku: rike vs hooldus
  - Probleemi tüüp (dropdown)
  - Tõsiduse valija (low/medium/high)
  - Kirjelduse tekstiväli
  - Foto üleslaadimise võimalus
  - "Saada teade" nupp
- **Responsive**: optimeeritud mobiiliseadmetele

### 2. Administraatori Töölaud (`/admin`)
- **Ülevaate kaardid**: 
  - Uued rikked
  - Plaanilised hooldused
  - Lõpetatud tööd
  - Varuosad tellimiseks
  - Aktiivsed projektid
- **Tabbed view**:
  - Rikked
  - Hooldused  
  - Arendusprojektid
- **Masinate grid**: kõik masinad koos staatusega
- **QR-koodide genereerimine**: masinate QR-koodide loomine ja printimine

### 3. Masina Detailvaade (`/machines/:id`)
- **Tabs**:
  - Ülevaade (põhiandmed)
  - Hooldused (ajalugu ja planeeritud)
  - Dokumendid (failide haldus)
  - Varuosad (seotud osad)
  - Arendusprojektid (masina projektid)
  - Statistika (rikete graafikud)
- **QR-koodi kuvamine**: masina QR-koodi näitamine ja printimise võimalus

### 4. Masinate Nimekiri (`/machines`)
- Filtreerimis- ja otsimisfunktsioonid
- Masinate staatuse ülevaade
- Kiirjuurdepääs masina detailvaatele

### 5. Hoolduste Haldus (`/maintenance`)
- Kalendervaade plaanilistele hooldustele
- Hoolduste loomine ja muutmine
- Töötajate määramine

### 6. Varuosade Haldus (`/parts`)
- Varuosade nimekiri koos laoajega
- Teavitused madala varuga osadest
- Uute osade lisamine

### 7. Arendusprojektide Haldus (`/projects`)
- Projektide nimekiri koos progressiga
- Uute projektide loomine
- Projektide seostamine masinatega

### 8. **Arendusprojektide Haldus** (`/projects`)
- Projektide nimekiri koos progressiga
- Uute projektide loomine
- Projektide seostamine masinatega

## Arendusmetoodika

### Test Driven Development (TDD)
Palun kasuta TDD meetodit kogu arendusprotsessi vältel:

1. **Red**: Kirjuta test, mis ebaõnnestub
2. **Green**: Kirjuta minimaalne kood, et test õnnestuks
3. **Refactor**: Paranda koodi kvaliteeti, hoides testid rohelised

### Testimise Stack
- **E2E testid**: Playwright (kasutajaliidese testid)
- **API testid**: Vitest või Jest (backend testid)
- **Unit testid**: Vitest (funktsionaalsed testid)

### Playwright Seadistus
```javascript
// playwright.config.js
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:8080',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'bun run start',
    port: 8080,
  },
});
```

### QR-koodide Töötamine
1. Iga masina jaoks genereeritakse QR-kood, mis sisaldab URL-i: `http://localhost:8080/operator/M-067`
2. QR-koodi skaneerimine avab operaatori vaate konkreetse masina jaoks
3. QR-koodid salvestatakse andmebaasi `machines` tabeli `qr_code_data` välja LONGBLOB formaadis
4. QR-koodide kuvamiseks API endpoint: `GET /api/files/qr/:machineId`

### Failide Haldus (Andmebaasipõhine)

Kõik failid (dokumendid, fotod, QR-koodid) salvestatakse andmebaasi LONGBLOB väljadesse:

#### Eelised:
- **Terviklik backup**: kõik andmed ühes kohas
- **Andmete terviklikkus**: ei ole kadunud faile
- **Turvalisus**: failidele juurdepääs ainult API kaudu
- **Versioonihaldus**: lihtne implementeerida
- **Migreerimine**: kõik andmed liiguvad koos

#### Implementatsioon:
1. **Failide üleslaadimine**: 
   - Bun.file() või multer multipart/form-data
   - Faili sisu salvestatakse LONGBLOB välja
   - Metadata (filename, size, mime_type) eraldi väljadesse

2. **Failide allalaadimine**:
   - API endpoint'id tagastavad faili andmed koos proper headeritega
   - Content-Type, Content-Disposition, Content-Length

3. **Optimeerimised**:
   - Pildivaatamiseks väikeste thumbnail'ide genereerimine
   - Caching headerite kasutamine
   - Suurte failide puhul streaming

#### API näited:
```javascript
// Dokumendi allalaadimine
GET /api/documents/123/download
Response headers:
Content-Type: application/pdf
Content-Disposition: attachment; filename="manual.pdf"
Content-Length: 1048576

// Foto kuvamine
GET /api/files/photo/456
Response headers:
Content-Type: image/jpeg
Content-Disposition: inline; filename="issue_photo.jpg"

// QR-koodi kuvamine
GET /api/files/qr/789
Response headers:
Content-Type: image/png
Content-Disposition: inline; filename="machine_M-067_qr.png"
```

### Teavitused
- Uued rikked: konsooli väljund (võib hiljem laiendada e-postiga)
- Madal varuosade varu: dashboard'i teavitused

### Seadistused
- Rakendus jookseb `localhost:8080` peal
- MySQL/MariaDB andmebaas `localhost:3306`
- Andmebaas: `cmms_db`
- Static failid serveeritakse `/public` kaustast
- Templates renderdatakse serveris (EJS)

## Failistruktuur (Fullstack)

```
cmms/
├── src/
│   ├── routes/
│   │   ├── pages.js        # HTML lehekülgede marsruudid
│   │   ├── api/
│   │   │   ├── machines.js
│   │   │   ├── issues.js
│   │   │   ├── maintenance.js
│   │   │   ├── parts.js
│   │   │   ├── projects.js
│   │   │   └── files.js
│   ├── models/
│   │   ├── Machine.js
│   │   ├── Issue.js
│   │   └── ...
│   ├── controllers/
│   │   ├── PageController.js
│   │   └── ApiController.js
│   ├── middleware/
│   ├── config/
│   │   └── database.js
│   ├── utils/
│   │   ├── fileHandler.js
│   │   └── qrGenerator.js
│   └── views/
│       ├── layouts/
│       │   └── main.ejs
│       ├── admin/
│       │   ├── dashboard.ejs
│       │   └── machines.ejs
│       ├── operator/
│       │   └── issue-form.ejs
│       └── partials/
│           ├── header.ejs
│           └── navigation.ejs
├── public/
│   ├── css/
│   │   └── tailwind.css
│   ├── js/
│   │   ├── main.js
│   │   └── alpine.min.js
│   └── images/
├── tests/
│   ├── e2e/
│   │   ├── operator.spec.js
│   │   ├── admin.spec.js
│   │   └── machines.spec.js
│   ├── unit/
│   │   ├── models/
│   │   └── controllers/
│   ├── fixtures/
│   │   └── test-data.sql
│   └── helpers/
├── database/
│   ├── schema.sql
│   ├── migrations/
│   └── seeds/
├── package.json
├── .env
├── server.js
├── playwright.config.js
└── README.md
```

## Algandmed

Palun loo testimiseks algandmed:
- 6 masinat erinevate andmetega
- 2-3 operaatorit
- Mõned näidisrikked ja hoolduskirjed
- Põhilised varuosad
- 1-2 arendusprojekti

## Keskkonna Seadistus

### .env fail (backend)
```
# Andmebaasi seadistused
DB_HOST=localhost
DB_PORT=3306
DB_NAME=cmms_db
DB_USER=cmms_user
DB_PASSWORD=cmms_password

# Serveri seadistused
PORT=8080
NODE_ENV=development

# Failihaldus
UPLOAD_PATH=./uploads  # Enam ei ole vajalik
MAX_FILE_SIZE=10485760  # 10MB maksimum failisuurus andmebaasi jaoks

# QR koodide seadistused
QR_CODE_SIZE=300
BASE_URL=http://localhost:8080
```

### Andmebaasi loomine
```sql
-- Loo andmebaas
CREATE DATABASE cmms_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Loo kasutaja (valikuline)
CREATE USER 'cmms_user'@'localhost' IDENTIFIED BY 'cmms_password';
GRANT ALL PRIVILEGES ON cmms_db.* TO 'cmms_user'@'localhost';
FLUSH PRIVILEGES;
```

## Käivitamine

### Eeldused
1. **Bun installitud**: https://bun.sh/
2. **MySQL/MariaDB server käivitatud**
3. **Andmebaas loodud** (cmms_db)

### Sammud
1. Rakenduse setup:
   ```bash
   bun install
   bun install -D @playwright/test vitest
   bun run migrate    # tabelite loomine
   bun run seed       # algandmete lisamine
   bun run test       # unit testid
   bun run dev        # development mode (port 8080)
   ```

2. E2E testid:
   ```bash
   npx playwright install chromium
   bun run test:e2e   # Playwright testid
   ```

3. TDD tsükkel:
   ```bash
   # 1. Kirjuta test (punane)
   bun run test:watch
   
   # 2. Implementeeri funktsioon (roheline)
   # 3. Refactoreeri (test jääb roheliseks)
   ```

4. Production:
   ```bash
   bun run build      # CSS ja JS optimeerimine
   bun run start      # production mode
   ```

## Prioriteedid ja TDD Järjekord

### 1. **Kõrge prioriteet** (algus TDD-ga)
**Alusta alati testiga, siis implementeeri:**

1. **Masinate CRUD** (US-001, US-002)
   - Test: masina lisamine
   - Test: QR-koodi genereerimine
   - Test: masina kustutamine

2. **Operaatori vaade** (US-003, US-004)
   - Test: QR-koodi URL-i avamine
   - Test: rikke registreerimine
   - Test: foto üleslaadimine

3. **Rikete haldus**
   - Test: uue rikke kuvamine admin vaates
   - Test: rikke staatuse muutmine

### 2. **Keskmine prioriteet**
4. **Hoolduste haldus** (US-005, US-006)
5. **Varuosade haldus** (US-007, US-008)
6. **Administraatori töölaud**

### 3. **Madal prioriteet**
7. **Arendusprojektid** (US-011, US-012)
8. **Statistika** (US-013)
9. **Täiustatud raportid**

### TDD Tsükkel iga funktsiooni jaoks:
```bash
# 1. Kirjuta E2E test (Playwright)
# 2. Kirjuta API test (Vitest)
# 3. Kirjuta unit test (Vitest)
# 4. Implementeeri backend
# 5. Implementeeri frontend
# 6. Refactoreeri, hoides testid rohelised
```

Palun järgi rangelt TDD meetodit - iga funktsioon peab algama testiga!