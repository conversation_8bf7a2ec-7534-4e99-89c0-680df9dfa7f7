import { pool } from '../config/database.js';

class Part {
  constructor(data) {
    this.id = data.id;
    this.part_number = data.part_number;
    this.name = data.name;
    this.description = data.description;
    this.category = data.category;
    this.manufacturer = data.manufacturer;
    this.model = data.model;
    this.unit_of_measure = data.unit_of_measure;
    this.unit_price = data.unit_price;
    this.supplier = data.supplier;
    this.supplier_part_number = data.supplier_part_number;
    this.location = data.location;
    this.quantity_in_stock = data.quantity_in_stock;
    this.minimum_stock_level = data.minimum_stock_level;
    this.maximum_stock_level = data.maximum_stock_level;
    this.reorder_point = data.reorder_point;
    this.is_active = data.is_active;
    this.notes = data.notes;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // <PERSON><PERSON><PERSON> varuosade leidmine
  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT p.*,
               CASE
                 WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 'low'
                 WHEN p.quantity_in_stock >= p.maximum_stock_level THEN 'high'
                 ELSE 'normal'
               END as stock_status,
               (SELECT COUNT(*) FROM machine_parts mp WHERE mp.part_id = p.id) as machine_count
        FROM parts p
        WHERE 1=1
      `;
      const params = [];

      // Filtreerimine
      if (filters.category) {
        query += ' AND p.category = ?';
        params.push(filters.category);
      }

      if (filters.is_active !== undefined) {
        query += ' AND p.is_active = ?';
        params.push(filters.is_active);
      }

      if (filters.low_stock) {
        query += ' AND p.quantity_in_stock <= p.minimum_stock_level';
      }

      if (filters.search) {
        query += ' AND (p.name LIKE ? OR p.part_number LIKE ? OR p.description LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // Sorteerimine
      const sortBy = filters.sort_by || 'name';
      const sortOrder = filters.sort_order || 'ASC';
      query += ` ORDER BY p.${sortBy} ${sortOrder}`;

      // Limit
      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(parseInt(filters.limit));
      }

      const [rows] = await pool.execute(query, params);
      return rows.map(row => new Part(row));
    } catch (error) {
      console.error('Error finding parts:', error);
      throw error;
    }
  }

  // Ühe varuosa leidmine ID järgi
  static async findById(id) {
    try {
      const query = `
        SELECT p.*,
               CASE
                 WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 'low'
                 WHEN p.quantity_in_stock >= p.maximum_stock_level THEN 'high'
                 ELSE 'normal'
               END as stock_status
        FROM parts p
        WHERE p.id = ?
      `;
      const [rows] = await pool.execute(query, [id]);
      return rows.length > 0 ? new Part(rows[0]) : null;
    } catch (error) {
      console.error('Error finding part by ID:', error);
      throw error;
    }
  }

  // Varuosa loomine
  static async create(data) {
    try {
      const query = `
        INSERT INTO parts (
          part_number, name, description, category, manufacturer, model,
          unit_of_measure, unit_price, supplier, supplier_part_number, location,
          quantity_in_stock, minimum_stock_level, maximum_stock_level, reorder_point,
          is_active, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        data.part_number,
        data.name,
        data.description || null,
        data.category,
        data.manufacturer || null,
        data.model || null,
        data.unit_of_measure || 'tk',
        data.unit_price || 0,
        data.supplier || null,
        data.supplier_part_number || null,
        data.location || null,
        data.quantity_in_stock || 0,
        data.minimum_stock_level || 0,
        data.maximum_stock_level || 0,
        data.reorder_point || 0,
        data.is_active !== undefined ? data.is_active : true,
        data.notes || null
      ];

      const [result] = await pool.execute(query, params);
      return await Part.findById(result.insertId);
    } catch (error) {
      console.error('Error creating part:', error);
      throw error;
    }
  }

  // Varuosa uuendamine
  static async update(id, data) {
    try {
      const query = `
        UPDATE parts SET
          part_number = ?, name = ?, description = ?, category = ?, manufacturer = ?, model = ?,
          unit_of_measure = ?, unit_price = ?, supplier = ?, supplier_part_number = ?, location = ?,
          quantity_in_stock = ?, minimum_stock_level = ?, maximum_stock_level = ?, reorder_point = ?,
          is_active = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      const params = [
        data.part_number,
        data.name,
        data.description || null,
        data.category,
        data.manufacturer || null,
        data.model || null,
        data.unit_of_measure || 'tk',
        data.unit_price || 0,
        data.supplier || null,
        data.supplier_part_number || null,
        data.location || null,
        data.quantity_in_stock || 0,
        data.minimum_stock_level || 0,
        data.maximum_stock_level || 0,
        data.reorder_point || 0,
        data.is_active !== undefined ? data.is_active : true,
        data.notes || null,
        id
      ];

      await pool.execute(query, params);
      return await Part.findById(id);
    } catch (error) {
      console.error('Error updating part:', error);
      throw error;
    }
  }

  // Laokoguse uuendamine
  static async updateStock(id, newQuantity, reason = null) {
    try {
      const query = `
        UPDATE parts SET
          quantity_in_stock = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      await pool.execute(query, [newQuantity, id]);

      // Logime laokoguse muutuse
      if (reason) {
        await Part.logStockChange(id, newQuantity, reason);
      }

      return await Part.findById(id);
    } catch (error) {
      console.error('Error updating stock:', error);
      throw error;
    }
  }

  // Laokoguse muutuse logimine
  static async logStockChange(partId, newQuantity, reason) {
    try {
      const query = `
        INSERT INTO part_usage_history (
          part_id, quantity_used, usage_type, description, used_by
        ) VALUES (?, ?, 'other', ?, 'System')
      `;
      await pool.execute(query, [partId, newQuantity, reason]);
    } catch (error) {
      console.error('Error logging stock change:', error);
    }
  }

  // Madala varuga varuosade leidmine
  static async findLowStock() {
    try {
      const query = `
        SELECT p.*,
               CASE
                 WHEN p.quantity_in_stock = 0 THEN 'out'
                 WHEN p.quantity_in_stock > 0 AND p.quantity_in_stock <= p.minimum_stock_level THEN 'low'
                 WHEN p.quantity_in_stock >= p.maximum_stock_level THEN 'high'
                 ELSE 'normal'
               END as stock_status
        FROM parts p
        WHERE p.quantity_in_stock > 0
          AND p.quantity_in_stock <= p.minimum_stock_level
          AND p.is_active = TRUE
        ORDER BY (p.quantity_in_stock - p.minimum_stock_level) ASC
      `;
      const [rows] = await pool.execute(query);
      return rows.map(row => new Part(row));
    } catch (error) {
      console.error('Error finding low stock parts:', error);
      throw error;
    }
  }

  // Kategooriate leidmine
  static async getCategories() {
    try {
      const query = `
        SELECT DISTINCT category, COUNT(*) as count
        FROM parts
        WHERE is_active = TRUE
        GROUP BY category
        ORDER BY category
      `;
      const [rows] = await pool.execute(query);
      return rows;
    } catch (error) {
      console.error('Error getting categories:', error);
      throw error;
    }
  }

  // Statistika
  static async getStatistics() {
    try {
      const query = `
        SELECT
          COUNT(*) as total_parts,
          SUM(quantity_in_stock * unit_price) as total_inventory_value,
          SUM(CASE WHEN quantity_in_stock > 0 AND quantity_in_stock <= minimum_stock_level THEN 1 ELSE 0 END) as low_stock_count,
          SUM(CASE WHEN quantity_in_stock = 0 THEN 1 ELSE 0 END) as out_of_stock_count,
          AVG(unit_price) as average_unit_price
        FROM parts
        WHERE is_active = TRUE
      `;
      const [rows] = await pool.execute(query);
      return rows[0];
    } catch (error) {
      console.error('Error getting parts statistics:', error);
      throw error;
    }
  }

  // Varuosa kustutamine (soft delete - arhiveerib)
  static async delete(id) {
    try {
      const query = 'UPDATE parts SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
      await pool.execute(query, [id]);
      return true;
    } catch (error) {
      console.error('Error archiving part:', error);
      throw error;
    }
  }

  // Varuosa taastamine arhiivist
  static async restore(id) {
    try {
      const query = 'UPDATE parts SET is_active = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
      await pool.execute(query, [id]);
      return await Part.findById(id);
    } catch (error) {
      console.error('Error restoring part:', error);
      throw error;
    }
  }

  // Arhiveeritud varuosade leidmine
  static async findArchived(filters = {}) {
    try {
      let query = `
        SELECT p.*,
               CASE
                 WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 'low'
                 WHEN p.quantity_in_stock >= p.maximum_stock_level THEN 'high'
                 ELSE 'normal'
               END as stock_status,
               (SELECT COUNT(*) FROM machine_parts mp WHERE mp.part_id = p.id) as machine_count
        FROM parts p
        WHERE p.is_active = FALSE
      `;
      const params = [];

      // Filtreerimine
      if (filters.category) {
        query += ' AND p.category = ?';
        params.push(filters.category);
      }

      if (filters.search) {
        query += ' AND (p.name LIKE ? OR p.part_number LIKE ? OR p.description LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // Sorteerimine
      const sortBy = filters.sort_by || 'updated_at';
      const sortOrder = filters.sort_order || 'DESC';
      query += ` ORDER BY p.${sortBy} ${sortOrder}`;

      // Limit
      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(parseInt(filters.limit));
      }

      const [rows] = await pool.execute(query, params);
      return rows.map(row => new Part(row));
    } catch (error) {
      console.error('Error finding archived parts:', error);
      throw error;
    }
  }
}

export default Part;
