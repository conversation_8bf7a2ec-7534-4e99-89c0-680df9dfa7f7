import { MaintenanceRequest } from '../models/MaintenanceRequest.js';
import { emailService } from './EmailService.js';

class NotificationScheduler {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.checkInterval = 60 * 60 * 1000; // Check every hour
  }

  start() {
    if (this.isRunning) {
      console.log('Notification scheduler is already running');
      return;
    }

    console.log('🔔 Starting notification scheduler...');
    this.isRunning = true;

    // Run immediately on start
    this.checkMaintenanceReminders();

    // Then run every hour
    this.intervalId = setInterval(() => {
      this.checkMaintenanceReminders();
    }, this.checkInterval);

    console.log('✅ Notification scheduler started');
  }

  stop() {
    if (!this.isRunning) {
      console.log('Notification scheduler is not running');
      return;
    }

    console.log('🛑 Stopping notification scheduler...');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log('✅ Notification scheduler stopped');
  }

  async checkMaintenanceReminders() {
    if (!process.env.MAINTENANCE_REMINDERS === 'true') {
      return;
    }

    try {
      console.log('🔍 Checking for maintenance reminders...');

      // Get maintenance requests scheduled for tomorrow
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      const dayAfterTomorrow = new Date(tomorrow);
      dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1);

      const upcomingMaintenance = await MaintenanceRequest.findAll({
        status: 'scheduled',
        scheduled_date_from: tomorrow.toISOString().split('T')[0],
        scheduled_date_to: dayAfterTomorrow.toISOString().split('T')[0],
      });

      console.log(`📋 Found ${upcomingMaintenance.length} maintenance requests for tomorrow`);

      for (const maintenanceRequest of upcomingMaintenance) {
        try {
          await emailService.notifyMaintenanceReminder(maintenanceRequest);
          console.log(`📧 Sent reminder for maintenance request #${maintenanceRequest.id}`);
        } catch (error) {
          console.error(
            `Failed to send reminder for maintenance request #${maintenanceRequest.id}:`,
            error
          );
        }
      }

      // Also check for overdue maintenance (scheduled for yesterday or earlier but not completed)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(23, 59, 59, 999);

      const overdueMaintenance = await MaintenanceRequest.findAll({
        status: 'scheduled',
        scheduled_date_to: yesterday.toISOString().split('T')[0],
      });

      if (overdueMaintenance.length > 0) {
        console.log(`⚠️ Found ${overdueMaintenance.length} overdue maintenance requests`);

        for (const maintenanceRequest of overdueMaintenance) {
          try {
            await this.notifyOverdueMaintenance(maintenanceRequest);
            console.log(
              `📧 Sent overdue notification for maintenance request #${maintenanceRequest.id}`
            );
          } catch (error) {
            console.error(
              `Failed to send overdue notification for maintenance request #${maintenanceRequest.id}:`,
              error
            );
          }
        }
      }
    } catch (error) {
      console.error('Error checking maintenance reminders:', error);
    }
  }

  async notifyOverdueMaintenance(maintenanceRequest) {
    const subject = `🚨 Hooldus hilinenud: ${maintenanceRequest.title}`;
    const html = this.generateOverdueMaintenanceHtml(maintenanceRequest);

    await emailService.sendEmail(
      process.env.MAINTENANCE_TEAM_EMAIL || '<EMAIL>',
      subject,
      html
    );
  }

  generateOverdueMaintenanceHtml(maintenanceRequest) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #EF4444; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .overdue { background: #FEE2E2; border: 1px solid #EF4444; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 Hooldus hilinenud</h1>
          </div>
          <div class="content">
            <div class="overdue">
              <h2>⚠️ Hooldus on hilinenud!</h2>
              <p><strong>${maintenanceRequest.title}</strong></p>
            </div>
            
            <p><strong>Masin:</strong> ${maintenanceRequest.machine_number} - ${maintenanceRequest.machine_name}</p>
            <p><strong>Planeeritud kuupäev:</strong> ${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</p>
            <p><strong>Määratud:</strong> ${maintenanceRequest.assigned_to || 'Määramata'}</p>
            <p><strong>Hilinemise aeg:</strong> ${Math.ceil((new Date() - new Date(maintenanceRequest.scheduled_date)) / (1000 * 60 * 60 * 24))} päeva</p>
            
            <p style="color: #EF4444; font-weight: bold;">🚨 Palun viige hooldus läbi esimesel võimalusel!</p>
            
            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/maintenance/${maintenanceRequest.id}" class="button">Vaata hoolduse detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Manual trigger methods for testing
  async sendTestNotification() {
    try {
      const testEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
      const subject = '🧪 CMMS Test Notification';
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10B981; color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🧪 CMMS Test Notification</h1>
            </div>
            <div class="content">
              <h2>Email service is working!</h2>
              <p>This is a test notification from the CMMS system.</p>
              <p><strong>Timestamp:</strong> ${new Date().toLocaleString('et-EE')}</p>
              <p>If you received this email, the notification system is configured correctly.</p>
            </div>
            <div class="footer">
              CMMS - Computerized Maintenance Management System
            </div>
          </div>
        </body>
        </html>
      `;

      const result = await emailService.sendEmail(testEmail, subject, html);
      console.log('Test notification sent:', result);
      return result;
    } catch (error) {
      console.error('Failed to send test notification:', error);
      return false;
    }
  }
}

export const notificationScheduler = new NotificationScheduler();
