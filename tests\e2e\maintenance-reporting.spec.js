import { test, expect } from '@playwright/test';

test.describe('Maintenance Reporting (US-006)', () => {
  test('should show maintenance reporting button in operator view', async ({ page }) => {
    await page.goto('/operator/M-DEMO');

    // Should show "Teata hoolduse vajadusest" button
    await expect(page.locator('[data-testid="report-maintenance-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="report-maintenance-btn"]')).toContainText(
      'Teata hoolduse vajadusest'
    );

    // Button should be orange/prominent
    await expect(page.locator('[data-testid="report-maintenance-btn"]')).toHaveClass(
      /bg-orange-500/
    );
  });

  test('should open maintenance reporting form', async ({ page }) => {
    await page.goto('/operator/M-DEMO');

    // Click "Teata hoolduse vajadusest" button
    await page.locator('[data-testid="report-maintenance-btn"]').click();

    // Should navigate to maintenance reporting form
    await expect(page).toHaveURL(/\/operator\/M-DEMO\/maintenance/);

    // Form should be visible
    await expect(page.locator('[data-testid="maintenance-form"]')).toBeVisible();
    await expect(page.locator('h1')).toContainText('Teata hoolduse vajadusest');
  });

  test('should have all required form fields', async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    // Check all required form fields
    await expect(page.locator('[name="operator_number"]')).toBeVisible();
    await expect(page.locator('[name="operator_name"]')).toBeVisible();
    await expect(page.locator('[name="maintenance_type"]')).toBeVisible();
    await expect(page.locator('[name="urgency"]')).toBeVisible();
    await expect(page.locator('[name="title"]')).toBeVisible();
    await expect(page.locator('[name="description"]')).toBeVisible();
    await expect(page.locator('[name="requested_date"]')).toBeVisible();

    // Submit button should be present
    await expect(page.locator('[data-testid="submit-maintenance"]')).toBeVisible();
    await expect(page.locator('[data-testid="submit-maintenance"]')).toContainText(
      'Saada hoolduse taotlus'
    );
  });

  test('should have correct maintenance type options', async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    const maintenanceTypeSelect = page.locator('[name="maintenance_type"]');

    // Check that all maintenance types are available
    await expect(maintenanceTypeSelect.locator('option[value="preventive"]')).toBeVisible();
    await expect(maintenanceTypeSelect.locator('option[value="corrective"]')).toBeVisible();
    await expect(maintenanceTypeSelect.locator('option[value="emergency"]')).toBeVisible();
    await expect(maintenanceTypeSelect.locator('option[value="inspection"]')).toBeVisible();
    await expect(maintenanceTypeSelect.locator('option[value="calibration"]')).toBeVisible();
    await expect(maintenanceTypeSelect.locator('option[value="cleaning"]')).toBeVisible();
    await expect(maintenanceTypeSelect.locator('option[value="other"]')).toBeVisible();
  });

  test('should have correct urgency options', async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    const urgencySelect = page.locator('[name="urgency"]');

    // Check that all urgency levels are available
    await expect(urgencySelect.locator('option[value="low"]')).toBeVisible();
    await expect(urgencySelect.locator('option[value="medium"]')).toBeVisible();
    await expect(urgencySelect.locator('option[value="high"]')).toBeVisible();
    await expect(urgencySelect.locator('option[value="urgent"]')).toBeVisible();
  });

  test('should validate required fields', async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    // Try to submit without filling required fields
    await page.locator('[data-testid="submit-maintenance"]').click();

    // Should show validation errors
    await expect(page.locator('.error-message')).toBeVisible();
  });

  test('should submit maintenance request successfully', async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    // Fill out the form
    await page.locator('[name="operator_number"]').fill('OP-MAINT-001');
    await page.locator('[name="operator_name"]').fill('Test Maintenance Operator');
    await page.locator('[name="maintenance_type"]').selectOption('preventive');
    await page.locator('[name="urgency"]').selectOption('medium');
    await page.locator('[name="title"]').fill('Scheduled Oil Change');
    await page
      .locator('[name="description"]')
      .fill('Regular preventive maintenance - oil change and filter replacement needed.');
    await page.locator('[name="requested_date"]').fill('2024-01-15');

    // Submit the form
    await page.locator('[data-testid="submit-maintenance"]').click();

    // Should show success message or redirect
    await expect(page.locator('.success-message')).toBeVisible();
  });

  test('should be mobile-friendly', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/operator/M-DEMO/maintenance');

    // Form should be responsive
    await expect(page.locator('[data-testid="maintenance-form"]')).toHaveCSS(
      'max-width',
      /375px|100%/
    );

    // Form fields should be touch-friendly
    const submitButton = page.locator('[data-testid="submit-maintenance"]');
    await expect(submitButton).toHaveCSS('min-height', /44px|48px/);
  });

  test('should handle form errors gracefully', async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    // Fill form with invalid data
    await page.locator('[name="operator_number"]').fill(''); // Empty required field
    await page.locator('[name="title"]').fill(''); // Empty required field

    await page.locator('[data-testid="submit-maintenance"]').click();

    // Should show error messages
    await expect(page.locator('.error-message')).toBeVisible();

    // Form should remain on the same page
    await expect(page).toHaveURL(/\/operator\/M-DEMO\/maintenance/);
  });

  test("should pre-fill today's date as default requested date", async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    // Should have today's date as default
    const today = new Date().toISOString().split('T')[0];
    await expect(page.locator('[name="requested_date"]')).toHaveValue(today);
  });

  test('should show different urgency colors', async ({ page }) => {
    await page.goto('/operator/M-DEMO/maintenance');

    const urgencySelect = page.locator('[name="urgency"]');

    // Check that urgency options have appropriate styling
    await expect(urgencySelect.locator('option[value="low"]')).toContainText('🟢');
    await expect(urgencySelect.locator('option[value="medium"]')).toContainText('🟡');
    await expect(urgencySelect.locator('option[value="high"]')).toContainText('🟠');
    await expect(urgencySelect.locator('option[value="urgent"]')).toContainText('🔴');
  });
});
