// Standard viewport meta tag for consistent mobile display
export function getViewportMeta() {
  return `<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">`;
}

// CSS for consistent mobile display across different devices and browsers
export function getMobileCSS() {
  return `
    <style>
      /* Ensure consistent mobile display */
      html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }

      body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* Prevent zoom on input focus on iOS */
      input[type="text"],
      input[type="email"],
      input[type="password"],
      input[type="number"],
      input[type="tel"],
      input[type="url"],
      input[type="search"],
      textarea,
      select {
        font-size: 16px !important;
      }

      /* Consistent touch targets */
      button,
      input[type="submit"],
      input[type="button"],
      a {
        min-height: 44px;
        min-width: 44px;
      }

      /* Remove tap highlight on mobile */
      * {
        -webkit-tap-highlight-color: transparent;
      }

      /* Ensure proper scaling */
      @media screen and (max-width: 768px) {
        .container {
          padding-left: 1rem;
          padding-right: 1rem;
        }
      }
    </style>
  `;
}

// Standard navigation component for all CMMS views
export function getNavigationHTML(currentPage = '', pageTitle = 'CMMS') {
  const navItems = [
    { href: '/admin', icon: 'fas fa-home', label: 'Dashboard', key: 'dashboard' },
    { href: '/machines', icon: 'fas fa-cogs', label: 'Masinad', key: 'machines' },
    { href: '/admin/machine-groups', icon: 'fas fa-layer-group', label: 'Grupid', key: 'groups' },
    { href: '/admin/reports', icon: 'fas fa-chart-bar', label: 'Aruanded', key: 'reports' },
    { href: '/admin/issues', icon: 'fas fa-exclamation-triangle', label: 'Rikked', key: 'issues' },
    { href: '/admin/maintenance', icon: 'fas fa-wrench', label: 'Hooldus', key: 'maintenance' },
    { href: '/admin/partners', icon: 'fas fa-handshake', label: 'Partnerid', key: 'partners' },
    { href: '/admin/parts', icon: 'fas fa-boxes', label: 'Varuosad', key: 'parts' },
    { href: '/admin/documents', icon: 'fas fa-file-alt', label: 'Dokumendid', key: 'documents' },
    { href: '/projects', icon: 'fas fa-project-diagram', label: 'Projektid', key: 'projects' },
  ];

  const getNavClass = key => {
    return currentPage === key
      ? 'bg-blue-800 px-3 py-2 rounded min-h-[44px] flex items-center'
      : 'hover:bg-blue-700 px-3 py-2 rounded min-h-[44px] flex items-center';
  };

  const getMobileNavClass = key => {
    return currentPage === key
      ? 'block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center'
      : 'block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center';
  };

  return `
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white">
      <div class="container mx-auto px-4">
        <!-- Desktop Navigation -->
        <div class="hidden md:flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold">${pageTitle}</h1>
          </div>
          <div class="flex space-x-4">
            ${navItems
              .map(
                item => `
              <a href="${item.href}" class="${getNavClass(item.key)}">
                <i class="${item.icon} mr-2"></i>${item.label}
              </a>
            `
              )
              .join('')}
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden">
          <div class="flex justify-between items-center py-3">
            <h1 class="text-lg font-bold">CMMS</h1>
            <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
              <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
            </button>
          </div>

          <!-- Mobile Menu -->
          <div id="mobile-menu" class="hidden pb-4">
            <div class="space-y-2">
              ${navItems
                .map(
                  item => `
                <a href="${item.href}" class="${getMobileNavClass(item.key)}">
                  <i class="${item.icon} mr-2"></i>${item.label}
                </a>
              `
                )
                .join('')}
            </div>
          </div>
        </div>
      </div>
    </nav>
  `;
}

// Mobile menu toggle script
export const mobileMenuScript = `
<script>
  // Mobile menu toggle
  function toggleMobileMenu() {
    const menu = document.getElementById('mobile-menu');
    const icon = document.getElementById('mobile-menu-icon');

    if (menu.classList.contains('hidden')) {
      menu.classList.remove('hidden');
      icon.classList.remove('fa-bars');
      icon.classList.add('fa-times');
    } else {
      menu.classList.add('hidden');
      icon.classList.remove('fa-times');
      icon.classList.add('fa-bars');
    }
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const menu = document.getElementById('mobile-menu');
    const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

    if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
      toggleMobileMenu();
    }
  });
</script>
`;
