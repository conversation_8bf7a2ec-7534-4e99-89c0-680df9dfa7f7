# Install MariaDB as Windows Service
# This script must be run as Administrator

Write-Host "Installing MariaDB as Windows Service..." -ForegroundColor Green

# Stop any running MariaDB processes
Write-Host "Stopping any running MariaDB processes..." -ForegroundColor Yellow
Get-Process mysqld -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Define paths
$mariadbPath = "C:\Program Files\MariaDB 11.7\bin\mysqld.exe"
$dataPath = "C:\Program Files\MariaDB 11.7\data"
$configPath = "C:\Program Files\MariaDB 11.7\data\my.ini"

# Check if MariaDB executable exists
if (-not (Test-Path $mariadbPath)) {
    Write-Host "ERROR: MariaDB executable not found at $mariadbPath" -ForegroundColor Red
    exit 1
}

# Install the service
Write-Host "Installing MariaDB service..." -ForegroundColor Yellow
try {
    # Remove existing service if it exists
    $existingService = Get-Service -Name "MariaDB" -ErrorAction SilentlyContinue
    if ($existingService) {
        Write-Host "Removing existing MariaDB service..." -ForegroundColor Yellow
        Stop-Service -Name "MariaDB" -Force -ErrorAction SilentlyContinue
        & sc.exe delete "MariaDB"
        Start-Sleep -Seconds 2
    }
    
    # Install new service
    $installCommand = "`"$mariadbPath`" --install MariaDB --defaults-file=`"$configPath`""
    Write-Host "Running: $installCommand" -ForegroundColor Cyan
    
    & cmd.exe /c $installCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "MariaDB service installed successfully!" -ForegroundColor Green
        
        # Start the service
        Write-Host "Starting MariaDB service..." -ForegroundColor Yellow
        Start-Service -Name "MariaDB"
        
        # Set service to start automatically
        Set-Service -Name "MariaDB" -StartupType Automatic
        
        Write-Host "MariaDB service is now running and set to start automatically!" -ForegroundColor Green
        
        # Check service status
        $service = Get-Service -Name "MariaDB"
        Write-Host "Service Status: $($service.Status)" -ForegroundColor Cyan
        
    } else {
        Write-Host "ERROR: Failed to install MariaDB service" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nMariaDB is now installed as a Windows service!" -ForegroundColor Green
Write-Host "- Service Name: MariaDB" -ForegroundColor Cyan
Write-Host "- Startup Type: Automatic" -ForegroundColor Cyan
Write-Host "- Status: Running" -ForegroundColor Cyan
Write-Host "`nThe database will now start automatically when Windows boots." -ForegroundColor Green
