-- Create maintenance_requests table for tracking maintenance needs
CREATE TABLE IF NOT EXISTS maintenance_requests (
  id INT AUTO_INCREMENT PRIMARY KEY,
  machine_id INT NOT NULL,
  operator_number VARCHAR(50) NOT NULL,
  operator_name VARCHAR(100),
  maintenance_type <PERSON>NUM('preventive', 'corrective', 'emergency', 'inspection', 'calibration', 'cleaning', 'other') NOT NULL DEFAULT 'preventive',
  urgency ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
  title VARCHAR(200) NOT NULL,
  description TEXT,
  status ENUM('requested', 'scheduled', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'requested',
  requested_date DATE,
  scheduled_date DATE NULL,
  completed_date DATE NULL,
  assigned_to VARCHAR(100),
  maintenance_notes TEXT,
  completion_notes TEXT,
  estimated_duration INT, -- in minutes
  actual_duration INT, -- in minutes
  cost DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
  INDEX idx_machine_id (machine_id),
  INDEX idx_status (status),
  INDEX idx_urgency (urgency),
  INDEX idx_requested_date (requested_date),
  INDEX idx_scheduled_date (scheduled_date)
);

-- Create maintenance_attachments table for photos and documents
CREATE TABLE IF NOT EXISTS maintenance_attachments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  maintenance_request_id INT NOT NULL,
  filename VARCHAR(255) NOT NULL,
  original_filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size INT,
  mime_type VARCHAR(100),
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE CASCADE,
  INDEX idx_maintenance_request_id (maintenance_request_id)
);

-- Create maintenance_history table for tracking maintenance activities
CREATE TABLE IF NOT EXISTS maintenance_history (
  id INT AUTO_INCREMENT PRIMARY KEY,
  machine_id INT NOT NULL,
  maintenance_request_id INT,
  maintenance_type ENUM('preventive', 'corrective', 'emergency', 'inspection', 'calibration', 'cleaning', 'other') NOT NULL,
  performed_by VARCHAR(100),
  performed_date DATE NOT NULL,
  duration_minutes INT,
  cost DECIMAL(10,2),
  description TEXT,
  parts_used TEXT,
  next_maintenance_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
  FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE SET NULL,
  INDEX idx_machine_id (machine_id),
  INDEX idx_performed_date (performed_date),
  INDEX idx_next_maintenance_date (next_maintenance_date)
);
