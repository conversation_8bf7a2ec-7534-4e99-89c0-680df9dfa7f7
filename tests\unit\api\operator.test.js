import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { app } from '../../../server.js';

describe('Operator API', () => {
  beforeEach(async () => {
    // Clean up test data
  });

  afterEach(async () => {
    // Clean up after tests
  });

  describe('GET /api/operator/:machineNumber', () => {
    it('should get machine info by machine number', async () => {
      // First create a test machine with unique number
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-OP-${timestamp}`,
        name: 'Test Machine for Operator',
        location: 'Test Location',
        status: 'online',
      };

      const createResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData),
      });

      expect(createResponse.status).toBe(201);

      // Now get machine info by machine number
      const response = await app.request(`/api/operator/M-OP-${timestamp}`);

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('machine_number', `M-OP-${timestamp}`);
      expect(result).toHaveProperty('name', 'Test Machine for Operator');
      expect(result).toHaveProperty('location', 'Test Location');
      expect(result).toHaveProperty('status', 'online');
    });

    it('should return 404 for non-existent machine', async () => {
      const response = await app.request('/api/operator/INVALID-MACHINE');

      expect(response.status).toBe(404);
      const result = await response.json();
      expect(result).toHaveProperty('error', 'Machine not found');
    });

    it('should return machine with current issues count', async () => {
      // This test will be expanded when we implement issues
      // Use an existing machine from previous tests
      const response = await app.request('/api/operator/M-DEMO');

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('open_issues_count');
      expect(typeof result.open_issues_count).toBe('number');
    });
  });
});
