import { Hono } from 'hono';
import { Issue } from '../../models/Issue.js';
import { IssueMaterial } from '../../models/IssueMaterial.js';
import { emailService } from '../../services/EmailService.js';

export const issueApiRoutes = new Hono();

// POST /api/issues - Create a new issue
issueApiRoutes.post('/', async c => {
  try {
    const body = await c.req.json();

    // Validate required fields
    const { machine_id, operator_number, title } = body;

    if (!machine_id || !operator_number || !title) {
      return c.json(
        {
          error: 'Missing required fields: machine_id, operator_number, title',
        },
        400
      );
    }

    const issue = await Issue.create(body);

    // Send email notification for new issue
    try {
      await emailService.notifyNewIssue(issue);
    } catch (error) {
      console.error('Failed to send new issue notification:', error);
      // Don't fail the request if email fails
    }

    return c.json(issue, 201);
  } catch (error) {
    console.error('Error creating issue:', error);
    return c.json({ error: 'Failed to create issue' }, 500);
  }
});

// POST /api/issues/report - Create issue with photo support (multipart form)
issueApiRoutes.post('/report', async c => {
  try {
    const formData = await c.req.formData();

    const machine_id = formData.get('machine_id');
    const operator_number = formData.get('operator_number');
    const operator_name = formData.get('operator_name');
    const issue_type = formData.get('issue_type');
    const priority = formData.get('priority');
    const title = formData.get('title');
    const description = formData.get('description');
    const photo = formData.get('photo');

    // Validate required fields
    if (!machine_id || !operator_number || !title || !issue_type || !priority) {
      return c.json(
        {
          success: false,
          error:
            'Puuduvad kohustuslikud väljad: machine_id, operator_number, title, issue_type, priority',
        },
        400
      );
    }

    // Prepare issue data
    const issueData = {
      machine_id: parseInt(machine_id),
      operator_number,
      operator_name: operator_name || null,
      issue_type,
      priority,
      title,
      description: description || null,
    };

    // Handle photo if provided
    if (photo && photo.size > 0) {
      // Convert file to buffer
      const photoBuffer = Buffer.from(await photo.arrayBuffer());

      // Check file size (max 10MB)
      if (photoBuffer.length > 10 * 1024 * 1024) {
        return c.json(
          {
            success: false,
            error: 'Foto on liiga suur (max 10MB)',
          },
          400
        );
      }

      // Check MIME type
      if (!photo.type.startsWith('image/')) {
        return c.json(
          {
            success: false,
            error: 'Fail peab olema pilt',
          },
          400
        );
      }

      // Add photo data to issue
      issueData.photo_filename = photo.name;
      issueData.photo_data = photoBuffer;
      issueData.photo_mime_type = photo.type;
      issueData.photo_size = photoBuffer.length;
    }

    const issue = await Issue.create(issueData);

    // Send email notification for new issue
    try {
      await emailService.notifyNewIssue(issue);
    } catch (error) {
      console.error('Failed to send new issue notification:', error);
      // Don't fail the request if email fails
    }

    return c.json(
      {
        success: true,
        data: issue,
        message: 'Rike teatis edukalt registreeritud',
      },
      201
    );
  } catch (error) {
    console.error('Error creating issue with photo:', error);
    return c.json(
      {
        success: false,
        error: 'Viga rikke teatamisel',
      },
      500
    );
  }
});

// GET /api/issues/statistics - Get issue statistics (must be before /:id route)
issueApiRoutes.get('/statistics', async c => {
  try {
    const stats = await Issue.getStatistics();
    return c.json(stats);
  } catch (error) {
    console.error('Error fetching issue statistics:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// GET /api/issues - Get all issues with optional filtering
issueApiRoutes.get('/', async c => {
  try {
    const query = c.req.query();
    const filters = {};

    // Parse query parameters for filtering
    if (query.machine_id) {
      filters.machine_id = parseInt(query.machine_id);
    }

    if (query.status) {
      filters.status = query.status;
    }

    if (query.priority) {
      filters.priority = query.priority;
    }

    const issues = await Issue.findAll(filters);
    return c.json(issues);
  } catch (error) {
    console.error('Error fetching issues:', error);
    return c.json({ error: 'Failed to fetch issues' }, 500);
  }
});

// GET /api/issues/:id/photo - Get issue photo
issueApiRoutes.get('/:id/photo', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const issue = await Issue.findById(id);

    if (!issue) {
      return c.json({ error: 'Issue not found' }, 404);
    }

    if (!issue.photo_data || !issue.photo_mime_type) {
      return c.json({ error: 'Photo not found' }, 404);
    }

    // Set proper headers for image display
    c.header('Content-Type', issue.photo_mime_type);
    c.header('Content-Length', issue.photo_data.length.toString());
    c.header('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour

    return c.body(issue.photo_data);
  } catch (error) {
    console.error('Error fetching issue photo:', error);
    return c.json({ error: 'Failed to fetch photo' }, 500);
  }
});

// GET /api/issues/:id - Get issue by ID
issueApiRoutes.get('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const issue = await Issue.findById(id);

    if (!issue) {
      return c.json({ error: 'Issue not found' }, 404);
    }

    return c.json(issue);
  } catch (error) {
    console.error('Error fetching issue:', error);
    return c.json({ error: 'Failed to fetch issue' }, 500);
  }
});

// PUT /api/issues/:id - Update issue
issueApiRoutes.put('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const updateData = await c.req.json();

    // Get current issue to compare status
    const currentIssue = await Issue.findById(id);
    if (!currentIssue) {
      return c.json({ error: 'Issue not found' }, 404);
    }

    const issue = await Issue.update(id, updateData);

    if (!issue) {
      return c.json({ error: 'Issue not found' }, 404);
    }

    // Send email notifications for status changes
    try {
      if (updateData.status && updateData.status !== currentIssue.status) {
        await emailService.notifyIssueStatusChange(issue, currentIssue.status, updateData.status);

        // Special notification for resolved issues
        if (updateData.status === 'resolved') {
          await emailService.notifyIssueResolved(issue);
        }

        // Notify partner if assigned
        if (issue.partner_email) {
          await emailService.notifyIssuePartnerStatusChange(
            issue,
            currentIssue.status,
            updateData.status
          );
        }
      }

      // Notify partner if newly assigned
      if (updateData.partner_id && updateData.partner_id !== currentIssue.partner_id) {
        await emailService.notifyIssuePartnerAssignment(issue);
      }
    } catch (error) {
      console.error('Failed to send issue status change notification:', error);
      // Don't fail the request if email fails
    }

    return c.json(issue);
  } catch (error) {
    console.error('Error updating issue:', error);
    return c.json({ error: 'Failed to update issue' }, 500);
  }
});

// DELETE /api/issues/:id - Delete issue
issueApiRoutes.delete('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await Issue.delete(id);

    if (!deleted) {
      return c.json({ error: 'Issue not found' }, 404);
    }

    return c.json({ message: 'Issue deleted successfully' });
  } catch (error) {
    console.error('Error deleting issue:', error);
    return c.json({ error: 'Failed to delete issue' }, 500);
  }
});

// ===== ISSUE MATERIALS ENDPOINTS =====

// GET /api/issues/:id/materials - Get issue materials
issueApiRoutes.get('/:id/materials', async c => {
  try {
    const issueId = parseInt(c.req.param('id'));
    const materials = await IssueMaterial.findByIssue(issueId);

    return c.json(materials);
  } catch (error) {
    console.error('Error fetching issue materials:', error);
    return c.json({ error: 'Failed to fetch materials' }, 500);
  }
});

// GET /api/issues/:id/materials/summary - Get issue materials summary
issueApiRoutes.get('/:id/materials/summary', async c => {
  try {
    const issueId = parseInt(c.req.param('id'));
    const summary = await IssueMaterial.getMaterialsSummary(issueId);

    return c.json(summary);
  } catch (error) {
    console.error('Error fetching materials summary:', error);
    return c.json({ error: 'Failed to fetch materials summary' }, 500);
  }
});

// POST /api/issues/:id/materials - Add material to issue
issueApiRoutes.post('/:id/materials', async c => {
  try {
    const issueId = parseInt(c.req.param('id'));
    const materialData = await c.req.json();

    // Add issue_id to the material data
    materialData.issue_id = issueId;

    const material = await IssueMaterial.create(materialData);

    return c.json(material, 201);
  } catch (error) {
    console.error('Error creating issue material:', error);
    return c.json({ error: error.message }, 400);
  }
});

// PUT /api/issues/:id/materials/:materialId - Update issue material
issueApiRoutes.put('/:id/materials/:materialId', async c => {
  try {
    const materialId = parseInt(c.req.param('materialId'));
    const materialData = await c.req.json();

    const material = await IssueMaterial.update(materialId, materialData);

    if (!material) {
      return c.json({ error: 'Material not found' }, 404);
    }

    return c.json(material);
  } catch (error) {
    console.error('Error updating issue material:', error);
    return c.json({ error: error.message }, 400);
  }
});

// DELETE /api/issues/:id/materials/:materialId - Delete issue material
issueApiRoutes.delete('/:id/materials/:materialId', async c => {
  try {
    const materialId = parseInt(c.req.param('materialId'));

    const deleted = await IssueMaterial.delete(materialId);

    if (!deleted) {
      return c.json({ error: 'Material not found' }, 404);
    }

    return c.json({ message: 'Material deleted successfully' });
  } catch (error) {
    console.error('Error deleting issue material:', error);
    return c.json({ error: 'Failed to delete material' }, 500);
  }
});
