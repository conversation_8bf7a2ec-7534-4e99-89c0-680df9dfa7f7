import { test, expect } from '@playwright/test';

test.describe('Issue Reporting (US-005)', () => {
  test('should show issue reporting button in operator view', async ({ page }) => {
    await page.goto('/operator/M-DEMO');

    // Should show "Teata rikest" button
    await expect(page.locator('[data-testid="report-issue-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="report-issue-btn"]')).toContainText('Teata rikest');

    // Button should be red/prominent
    await expect(page.locator('[data-testid="report-issue-btn"]')).toHaveClass(/bg-red-500/);
  });

  test('should open issue reporting form', async ({ page }) => {
    await page.goto('/operator/M-DEMO');

    // Click "Teata rikest" button
    await page.locator('[data-testid="report-issue-btn"]').click();

    // Should navigate to issue reporting form
    await expect(page).toHaveURL(/\/operator\/M-DEMO\/issue/);

    // Form should be visible
    await expect(page.locator('[data-testid="issue-form"]')).toBeVisible();
    await expect(page.locator('h1')).toContainText('Teata rikest');
  });

  test('should have all required form fields', async ({ page }) => {
    await page.goto('/operator/M-DEMO/issue');

    // Check all required form fields
    await expect(page.locator('[name="operator_number"]')).toBeVisible();
    await expect(page.locator('[name="operator_name"]')).toBeVisible();
    await expect(page.locator('[name="issue_type"]')).toBeVisible();
    await expect(page.locator('[name="priority"]')).toBeVisible();
    await expect(page.locator('[name="title"]')).toBeVisible();
    await expect(page.locator('[name="description"]')).toBeVisible();

    // Submit button should be present
    await expect(page.locator('[data-testid="submit-issue"]')).toBeVisible();
    await expect(page.locator('[data-testid="submit-issue"]')).toContainText('Saada rike teatis');
  });

  test('should have correct issue type options', async ({ page }) => {
    await page.goto('/operator/M-DEMO/issue');

    const issueTypeSelect = page.locator('[name="issue_type"]');

    // Check that all issue types are available
    await expect(issueTypeSelect.locator('option[value="mechanical"]')).toBeVisible();
    await expect(issueTypeSelect.locator('option[value="electrical"]')).toBeVisible();
    await expect(issueTypeSelect.locator('option[value="software"]')).toBeVisible();
    await expect(issueTypeSelect.locator('option[value="safety"]')).toBeVisible();
    await expect(issueTypeSelect.locator('option[value="quality"]')).toBeVisible();
    await expect(issueTypeSelect.locator('option[value="other"]')).toBeVisible();
  });

  test('should have correct priority options', async ({ page }) => {
    await page.goto('/operator/M-DEMO/issue');

    const prioritySelect = page.locator('[name="priority"]');

    // Check that all priority levels are available
    await expect(prioritySelect.locator('option[value="low"]')).toBeVisible();
    await expect(prioritySelect.locator('option[value="medium"]')).toBeVisible();
    await expect(prioritySelect.locator('option[value="high"]')).toBeVisible();
    await expect(prioritySelect.locator('option[value="critical"]')).toBeVisible();
  });

  test('should validate required fields', async ({ page }) => {
    await page.goto('/operator/M-DEMO/issue');

    // Try to submit without filling required fields
    await page.locator('[data-testid="submit-issue"]').click();

    // Should show validation errors
    await expect(page.locator('.error-message')).toBeVisible();
  });

  test('should submit issue successfully', async ({ page }) => {
    await page.goto('/operator/M-DEMO/issue');

    // Fill out the form
    await page.locator('[name="operator_number"]').fill('OP-TEST-001');
    await page.locator('[name="operator_name"]').fill('Test Operator');
    await page.locator('[name="issue_type"]').selectOption('mechanical');
    await page.locator('[name="priority"]').selectOption('high');
    await page.locator('[name="title"]').fill('Test Issue Report');
    await page
      .locator('[name="description"]')
      .fill('This is a test issue description for E2E testing.');

    // Submit the form
    await page.locator('[data-testid="submit-issue"]').click();

    // Should show success message or redirect
    await expect(page.locator('.success-message')).toBeVisible();
    // Or check for redirect to confirmation page
    // await expect(page).toHaveURL(/\/operator\/M-DEMO\/issue\/success/);
  });

  test('should be mobile-friendly', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/operator/M-DEMO/issue');

    // Form should be responsive
    await expect(page.locator('[data-testid="issue-form"]')).toHaveCSS('max-width', /375px|100%/);

    // Form fields should be touch-friendly
    const submitButton = page.locator('[data-testid="submit-issue"]');
    await expect(submitButton).toHaveCSS('min-height', /44px|48px/);
  });

  test('should handle form errors gracefully', async ({ page }) => {
    await page.goto('/operator/M-DEMO/issue');

    // Fill form with invalid data
    await page.locator('[name="operator_number"]').fill(''); // Empty required field
    await page.locator('[name="title"]').fill(''); // Empty required field

    await page.locator('[data-testid="submit-issue"]').click();

    // Should show error messages
    await expect(page.locator('.error-message')).toBeVisible();

    // Form should remain on the same page
    await expect(page).toHaveURL(/\/operator\/M-DEMO\/issue/);
  });
});
