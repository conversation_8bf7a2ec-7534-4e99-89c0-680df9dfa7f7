# CMMS Environment Configuration
# Copy this file to .env and update the values for your environment

# Andmebaasi seadistused / Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=cmms_db
DB_USER=root
DB_PASSWORD=your_database_password_here

# Serveri seadistused / Server Configuration
PORT=8080
NODE_ENV=development

# Rakenduse URL / Application URL
BASE_URL=http://localhost:8080

# Failihaldus / File Management
MAX_FILE_SIZE=10485760

# QR koodide seadistused / QR Code Settings
QR_CODE_SIZE=300

# Turvalisus / Security (for production)
# SESSION_SECRET=your_session_secret_here
# JWT_SECRET=your_jwt_secret_here

# E-posti seadistused / Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=CMMS System <<EMAIL>>

# Teavituste seadistused / Notification Settings
NOTIFICATIONS_ENABLED=true
ADMIN_EMAIL=<EMAIL>
MAINTENANCE_TEAM_EMAIL=<EMAIL>
CRITICAL_ISSUE_NOTIFICATIONS=true
MAINTENANCE_REMINDERS=true

# Logimise seadistused / Logging Configuration
# LOG_LEVEL=info
# LOG_FILE=logs/cmms.log

# Varukoopia seadistused / Backup Configuration (optional)
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30

# Arenduse seadistused / Development Settings
# DEBUG=true
# ENABLE_CORS=true

# Tootmise seadistused / Production Settings (uncomment for production)
# NODE_ENV=production
# HTTPS_ENABLED=true
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem
