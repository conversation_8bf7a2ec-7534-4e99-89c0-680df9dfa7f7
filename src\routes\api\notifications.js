import { Hono } from 'hono';
import { emailService } from '../../services/EmailService.js';
import { notificationScheduler } from '../../services/NotificationScheduler.js';

export const notificationApiRoutes = new Hono();

// GET /api/notifications/test - Send test notification
notificationApiRoutes.get('/test', async (c) => {
  try {
    const result = await notificationScheduler.sendTestNotification();
    
    if (result) {
      return c.json({ 
        success: true, 
        message: 'Test notification sent successfully' 
      });
    } else {
      return c.json({ 
        success: false, 
        message: 'Failed to send test notification' 
      }, 500);
    }
  } catch (error) {
    console.error('Error sending test notification:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to send test notification' 
    }, 500);
  }
});

// GET /api/notifications/status - Get notification service status
notificationApiRoutes.get('/status', async (c) => {
  try {
    const status = {
      email_service_enabled: emailService.isEnabled,
      scheduler_running: notificationScheduler.isRunning,
      smtp_host: process.env.SMTP_HOST || 'Not configured',
      from_email: process.env.FROM_EMAIL || 'Not configured',
      admin_email: process.env.ADMIN_EMAIL || 'Not configured',
      maintenance_team_email: process.env.MAINTENANCE_TEAM_EMAIL || 'Not configured',
      notifications_enabled: process.env.NOTIFICATIONS_ENABLED === 'true',
      maintenance_reminders: process.env.MAINTENANCE_REMINDERS === 'true',
      critical_issue_notifications: process.env.CRITICAL_ISSUE_NOTIFICATIONS === 'true'
    };
    
    return c.json(status);
  } catch (error) {
    console.error('Error getting notification status:', error);
    return c.json({ error: 'Failed to get notification status' }, 500);
  }
});

// POST /api/notifications/check-reminders - Manually trigger reminder check
notificationApiRoutes.post('/check-reminders', async (c) => {
  try {
    await notificationScheduler.checkMaintenanceReminders();
    
    return c.json({ 
      success: true, 
      message: 'Maintenance reminders check completed' 
    });
  } catch (error) {
    console.error('Error checking maintenance reminders:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to check maintenance reminders' 
    }, 500);
  }
});

// POST /api/notifications/start-scheduler - Start notification scheduler
notificationApiRoutes.post('/start-scheduler', async (c) => {
  try {
    notificationScheduler.start();
    
    return c.json({ 
      success: true, 
      message: 'Notification scheduler started' 
    });
  } catch (error) {
    console.error('Error starting notification scheduler:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to start notification scheduler' 
    }, 500);
  }
});

// POST /api/notifications/stop-scheduler - Stop notification scheduler
notificationApiRoutes.post('/stop-scheduler', async (c) => {
  try {
    notificationScheduler.stop();
    
    return c.json({ 
      success: true, 
      message: 'Notification scheduler stopped' 
    });
  } catch (error) {
    console.error('Error stopping notification scheduler:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to stop notification scheduler' 
    }, 500);
  }
});

// POST /api/notifications/send-custom - Send custom notification
notificationApiRoutes.post('/send-custom', async (c) => {
  try {
    const { to, subject, message } = await c.req.json();
    
    if (!to || !subject || !message) {
      return c.json({ 
        success: false, 
        error: 'Missing required fields: to, subject, message' 
      }, 400);
    }
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📧 CMMS Notification</h1>
          </div>
          <div class="content">
            <div style="white-space: pre-wrap;">${message}</div>
            <hr style="margin: 20px 0; border: none; border-top: 1px solid #ddd;">
            <p style="font-size: 12px; color: #666;">
              <strong>Sent:</strong> ${new Date().toLocaleString('et-EE')}<br>
              <strong>From:</strong> CMMS Administration
            </p>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
    
    const result = await emailService.sendEmail(to, subject, html);
    
    if (result) {
      return c.json({ 
        success: true, 
        message: 'Custom notification sent successfully' 
      });
    } else {
      return c.json({ 
        success: false, 
        message: 'Failed to send custom notification' 
      }, 500);
    }
  } catch (error) {
    console.error('Error sending custom notification:', error);
    return c.json({ 
      success: false, 
      error: 'Failed to send custom notification' 
    }, 500);
  }
});
