import { test, expect } from '@playwright/test';

test.describe('Machine Deletion', () => {
  test('should show delete button in machine detail view', async ({ page }) => {
    await page.goto('/machines/5');
    
    // Should show delete button
    await expect(page.locator('[data-testid="delete-machine"]')).toBeVisible();
    await expect(page.locator('[data-testid="delete-machine"]')).toContainText('Kustuta masin');
    
    // Button should be red
    await expect(page.locator('[data-testid="delete-machine"]')).toHaveClass(/bg-red-500/);
  });

  test('should show confirmation dialogs when deleting machine', async ({ page }) => {
    // Create a test machine first
    const testMachine = {
      machine_number: 'M-DELETE-TEST',
      name: 'Test Machine for Deletion',
      location: 'Test Location',
      status: 'online'
    };

    const createResponse = await page.request.post('/api/machines', {
      data: testMachine
    });
    expect(createResponse.ok()).toBeTruthy();
    const machine = await createResponse.json();

    // Go to machine detail page
    await page.goto(`/machines/${machine.id}`);

    // Set up dialog handlers
    let firstDialogShown = false;
    let secondDialogShown = false;
    
    page.on('dialog', async dialog => {
      if (!firstDialogShown) {
        expect(dialog.message()).toContain('Kas olete kindel');
        expect(dialog.message()).toContain('M-DELETE-TEST');
        await dialog.accept();
        firstDialogShown = true;
      } else if (!secondDialogShown) {
        expect(dialog.message()).toContain('VIIMANE HOIATUS');
        expect(dialog.message()).toContain('M-DELETE-TEST');
        await dialog.dismiss(); // Cancel the second dialog for this test
        secondDialogShown = true;
      }
    });

    // Click delete button
    await page.locator('[data-testid="delete-machine"]').click();

    // Wait a bit for dialogs to be processed
    await page.waitForTimeout(1000);

    // Verify both dialogs were shown
    expect(firstDialogShown).toBeTruthy();
    expect(secondDialogShown).toBeTruthy();

    // Machine should still exist since we cancelled
    const checkResponse = await page.request.get(`/api/machines/${machine.id}`);
    expect(checkResponse.ok()).toBeTruthy();

    // Clean up - delete the test machine
    await page.request.delete(`/api/machines/${machine.id}`);
  });

  test('should delete machine when both confirmations are accepted', async ({ page }) => {
    // Create a test machine first
    const testMachine = {
      machine_number: 'M-DELETE-CONFIRM-TEST',
      name: 'Test Machine for Confirmed Deletion',
      location: 'Test Location',
      status: 'online'
    };

    const createResponse = await page.request.post('/api/machines', {
      data: testMachine
    });
    expect(createResponse.ok()).toBeTruthy();
    const machine = await createResponse.json();

    // Go to machine detail page
    await page.goto(`/machines/${machine.id}`);

    // Set up dialog handlers to accept both
    page.on('dialog', async dialog => {
      await dialog.accept();
    });

    // Click delete button
    await page.locator('[data-testid="delete-machine"]').click();

    // Should redirect to admin dashboard
    await expect(page).toHaveURL('/admin');

    // Machine should no longer exist
    const checkResponse = await page.request.get(`/api/machines/${machine.id}`);
    expect(checkResponse.status()).toBe(404);
  });

  test('should handle deletion errors gracefully', async ({ page }) => {
    await page.goto('/machines/999999'); // Non-existent machine
    
    // Should show 404 or error message
    await expect(page.locator('body')).toContainText(/not found|404/i);
  });
});
