import { Hono } from 'hono';
import Project from '../../models/Project.js';

export const projectApiRoutes = new Hono();

// Specific routes first (before parameterized routes)

// GET /api/projects/statistics - Get project statistics
projectApiRoutes.get('/statistics', async c => {
  try {
    const stats = await Project.getStatistics();
    return c.json(stats);
  } catch (error) {
    console.error('Error fetching project statistics:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// GET /api/projects - Get all projects
projectApiRoutes.get('/', async c => {
  try {
    const query = c.req.query();
    const filters = {};

    if (query.status) {
      filters.status = query.status;
    }

    if (query.search) {
      filters.search = query.search;
    }

    const projects = await Project.findAll(filters);
    return c.json({
      success: true,
      data: projects,
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to fetch projects',
      },
      500
    );
  }
});

// POST /api/projects - Create new project
projectApiRoutes.post('/', async c => {
  try {
    const body = await c.req.json();
    const project = await Project.create(body);

    return c.json(
      {
        success: true,
        data: project,
        message: 'Projekt edukalt loodud',
      },
      201
    );
  } catch (error) {
    console.error('Error creating project:', error);
    return c.json(
      {
        success: false,
        error: error.message || 'Failed to create project',
      },
      400
    );
  }
});

// GET /api/projects/:id - Get project by ID
projectApiRoutes.get('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const project = await Project.findByIdWithMachines(id);

    if (!project) {
      return c.json(
        {
          success: false,
          error: 'Project not found',
        },
        404
      );
    }

    return c.json({
      success: true,
      data: project,
    });
  } catch (error) {
    console.error('Error fetching project:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to fetch project',
      },
      500
    );
  }
});

// PUT /api/projects/:id - Update project
projectApiRoutes.put('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();

    const project = await Project.update(id, body);

    if (!project) {
      return c.json(
        {
          success: false,
          error: 'Project not found',
        },
        404
      );
    }

    return c.json({
      success: true,
      data: project,
      message: 'Projekt edukalt uuendatud',
    });
  } catch (error) {
    console.error('Error updating project:', error);
    return c.json(
      {
        success: false,
        error: error.message || 'Failed to update project',
      },
      400
    );
  }
});

// PUT /api/projects/:id/progress - Update project progress
projectApiRoutes.put('/:id/progress', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();

    const project = await Project.updateProgress(id, body);

    if (!project) {
      return c.json(
        {
          success: false,
          error: 'Project not found',
        },
        404
      );
    }

    return c.json({
      success: true,
      data: project,
      message: 'Progress uuendatud',
    });
  } catch (error) {
    console.error('Error updating project progress:', error);
    return c.json(
      {
        success: false,
        error: error.message || 'Failed to update progress',
      },
      400
    );
  }
});

// DELETE /api/projects/:id - Delete project
projectApiRoutes.delete('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const result = await Project.delete(id);

    if (!result) {
      return c.json(
        {
          success: false,
          error: 'Project not found',
        },
        404
      );
    }

    return c.json({
      success: true,
      message: 'Projekt edukalt kustutatud',
    });
  } catch (error) {
    console.error('Error deleting project:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to delete project',
      },
      500
    );
  }
});
