import { test, expect } from '@playwright/test';

test.describe('Operator Interface (US-004)', () => {
  test('should open operator view via QR code', async ({ page }) => {
    // QR-kood suunab GET /operator/M-DEMO lehele
    await page.goto('/operator/M-DEMO');

    // EJS template sisaldab masina andmeid serverist
    await expect(page.locator('[data-testid="machine-info"]')).toContainText('M-DEMO');
    await expect(page.locator('[data-testid="machine-name"]')).toContainText('Demo Masin');

    // Esimene input on focused (progressive enhancement)
    await expect(page.locator('[name="operator_number"]')).toBeFocused();

    // Mobiilsõbralik Tailwind CSS
    await expect(page.locator('body')).toHaveClass(/max-w-md|w-full/);
  });

  test('should display machine status and basic info', async ({ page }) => {
    await page.goto('/operator/M-DEMO');

    // Machine information should be visible
    await expect(page.locator('[data-testid="machine-number"]')).toContainText('M-DEMO');
    await expect(page.locator('[data-testid="machine-status"]')).toBeVisible();
    await expect(page.locator('[data-testid="machine-location"]')).toBeVisible();

    // Should show current status
    await expect(page.locator('[data-testid="machine-status"]')).toContainText(/online|offline|maintenance/);
  });

  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/operator/M-DEMO');

    // Should be mobile-friendly
    await expect(page.locator('.container')).toHaveCSS('max-width', /375px|100%/);

    // Touch-friendly buttons
    const buttons = page.locator('button, input[type="submit"]');
    await expect(buttons.first()).toHaveCSS('min-height', /44px|48px/);
  });

  test('should handle non-existent machine gracefully', async ({ page }) => {
    await page.goto('/operator/INVALID-MACHINE');

    // Should show error message
    await expect(page.locator('.alert-error')).toContainText('Masinat ei leitud');
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
  });
});
