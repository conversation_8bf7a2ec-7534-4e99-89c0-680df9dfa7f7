#!/bin/bash

# CMMS Project Setup Script
# This script helps set up the development environment

set -e

echo "🚀 Setting up CMMS development environment..."

# Check if Bun is installed
if ! command -v bun &> /dev/null; then
    echo "❌ Bun is not installed. Installing Bun..."
    curl -fsSL https://bun.sh/install | bash
    export BUN_INSTALL="$HOME/.bun"
    export PATH="$BUN_INSTALL/bin:$PATH"
    echo "✅ Bun installed successfully"
else
    echo "✅ Bun is already installed"
fi

# Install dependencies
echo "📦 Installing dependencies..."
bun install

# Setup environment file
if [ ! -f .env ]; then
    echo "⚙️ Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please update it with your database credentials."
else
    echo "✅ .env file already exists"
fi

# Check if MariaDB/MySQL is running
echo "🗄️ Checking database connection..."
if command -v mysql &> /dev/null; then
    echo "✅ MySQL/MariaDB client found"
    
    # Try to connect to database
    if mysql -h localhost -u root -e "SELECT 1;" &> /dev/null; then
        echo "✅ Database connection successful"
        
        # Run migrations
        echo "🔄 Running database migrations..."
        bun run migrate
        echo "✅ Database migrations completed"
    else
        echo "⚠️ Cannot connect to database. Please:"
        echo "   1. Start your MariaDB/MySQL server"
        echo "   2. Update .env with correct database credentials"
        echo "   3. Run 'bun run migrate' to create tables"
    fi
else
    echo "⚠️ MySQL/MariaDB client not found. Please install MariaDB or MySQL"
fi

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
bunx playwright install chromium

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update .env with your database credentials"
echo "2. Start the development server: bun run dev"
echo "3. Open http://localhost:8080 in your browser"
echo ""
echo "Available commands:"
echo "  bun run dev        - Start development server"
echo "  bun run test       - Run API tests"
echo "  bun run test:e2e   - Run E2E tests"
echo "  bun run migrate    - Run database migrations"
echo ""
