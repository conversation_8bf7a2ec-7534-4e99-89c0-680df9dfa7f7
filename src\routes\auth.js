import { Hono } from 'hono';
import { User, Session, LoginAttempt } from '../models/index.js';
import { loginRateLimit, redirectIfAuthenticated } from '../middleware/auth.js';

const authRoutes = new Hono();

// Login page
authRoutes.get('/', redirectIfAuthenticated, async c => {
  const error = c.req.query('error');
  const message = c.req.query('message');
  
  return c.html(`
    <!DOCTYPE html>
    <html lang="et">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Sisselogimine - CMMS</title>
      <script src="https://cdn.tailwindcss.com"></script>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    </head>
    <body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
      <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
          <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4">
              <i class="fas fa-cogs text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900">CMMS Sisselogimine</h2>
            <p class="mt-2 text-sm text-gray-600">
              Sisestage oma kasutajaandmed süsteemi sisselogimiseks
            </p>
          </div>
          
          ${error ? `
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-red-800">${error}</p>
                </div>
              </div>
            </div>
          ` : ''}
          
          ${message ? `
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-blue-800">${message}</p>
                </div>
              </div>
            </div>
          ` : ''}
          
          <form class="mt-8 space-y-6" action="/auth" method="POST">
            <div class="space-y-4">
              <div>
                <label for="username" class="block text-sm font-medium text-gray-700">
                  Kasutajanimi
                </label>
                <div class="mt-1 relative">
                  <input id="username" 
                         name="username" 
                         type="text" 
                         required 
                         class="appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                         placeholder="Sisestage kasutajanimi"
                         data-testid="username-input">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-user text-gray-400"></i>
                  </div>
                </div>
              </div>
              
              <div>
                <label for="password" class="block text-sm font-medium text-gray-700">
                  Parool
                </label>
                <div class="mt-1 relative">
                  <input id="password" 
                         name="password" 
                         type="password" 
                         required 
                         class="appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                         placeholder="Sisestage parool"
                         data-testid="password-input">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <button type="submit" 
                      data-testid="login-submit"
                      class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                  <i class="fas fa-sign-in-alt text-blue-500 group-hover:text-blue-400"></i>
                </span>
                Logi sisse
              </button>
            </div>
          </form>
          
          <div class="text-center">
            <p class="text-xs text-gray-500">
              CMMS - Computerized Maintenance Management System
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Login POST handler
authRoutes.post('/', loginRateLimit, async c => {
  try {
    const body = await c.req.parseBody();
    const { username, password } = body;
    
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    const userAgent = c.req.header('user-agent') || '';
    
    // Validate input
    if (!username || !password) {
      await LoginAttempt.logAttempt(username, ipAddress, userAgent, false, 'Missing credentials');
      return c.redirect('/login?error=' + encodeURIComponent('Kasutajanimi ja parool on kohustuslikud'));
    }
    
    // Find user
    const user = await User.findByUsername(username);
    
    if (!user) {
      await LoginAttempt.logAttempt(username, ipAddress, userAgent, false, 'User not found');
      return c.redirect('/login?error=' + encodeURIComponent('Vale kasutajanimi või parool'));
    }
    
    // Check if account is locked
    if (user.isLocked()) {
      await LoginAttempt.logAttempt(username, ipAddress, userAgent, false, 'Account locked');
      return c.redirect('/login?error=' + encodeURIComponent('Konto on ajutiselt lukustatud. Palun proovige hiljem uuesti.'));
    }
    
    // Validate password
    const isValidPassword = await user.validatePassword(password);
    
    if (!isValidPassword) {
      await user.incrementFailedAttempts();
      await LoginAttempt.logAttempt(username, ipAddress, userAgent, false, 'Invalid password');
      return c.redirect('/login?error=' + encodeURIComponent('Vale kasutajanimi või parool'));
    }
    
    // Reset failed attempts and update last login
    await user.resetFailedAttempts();
    
    // Create session
    const session = await Session.createSession(user.id, ipAddress, userAgent);
    
    // Log successful login
    await LoginAttempt.logAttempt(username, ipAddress, userAgent, true);
    
    // Set session cookie
    c.res.headers.set('Set-Cookie', `session_id=${session.id}; Path=/; HttpOnly; Max-Age=${8 * 60 * 60}; SameSite=Lax`);
    
    // Redirect based on role
    const redirectUrl = user.role === 'admin' ? '/admin' : 
                       user.role === 'maintenance' ? '/maintenance' :
                       user.role === 'operator' ? '/operator' : '/viewer';
    
    return c.redirect(redirectUrl);
    
  } catch (error) {
    console.error('Login error:', error);
    return c.redirect('/login?error=' + encodeURIComponent('Sisselogimisel tekkis viga. Palun proovige uuesti.'));
  }
});

// Logout handler
authRoutes.post('/logout', async c => {
  try {
    const sessionId = c.req.cookie('session_id');
    
    if (sessionId) {
      // Destroy session from database
      await Session.destroy({ where: { id: sessionId } });
    }
    
    // Clear session cookie
    c.res.headers.set('Set-Cookie', 'session_id=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly');
    
    return c.redirect('/login?message=' + encodeURIComponent('Olete edukalt välja logitud'));
    
  } catch (error) {
    console.error('Logout error:', error);
    return c.redirect('/login');
  }
});

// GET logout (for convenience)
authRoutes.get('/logout', async c => {
  try {
    const sessionId = c.req.cookie('session_id');
    
    if (sessionId) {
      await Session.destroy({ where: { id: sessionId } });
    }
    
    c.res.headers.set('Set-Cookie', 'session_id=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly');
    
    return c.redirect('/login?message=' + encodeURIComponent('Olete edukalt välja logitud'));
    
  } catch (error) {
    console.error('Logout error:', error);
    return c.redirect('/login');
  }
});

// Logout
authRoutes.get('/logout', async c => {
  try {
    // Get session from cookie
    const sessionId = c.req.cookie('session_id');

    if (sessionId) {
      // Delete session from database
      await Session.destroy({
        where: { id: sessionId }
      });
    }

    // Clear session cookie
    c.cookie('session_id', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0 // Expire immediately
    });

    return c.redirect('/login?message=' + encodeURIComponent('Olete edukalt välja logitud'));

  } catch (error) {
    console.error('Logout error:', error);
    return c.redirect('/login');
  }
});

export { authRoutes };
