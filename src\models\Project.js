import { pool } from '../config/database.js';

class Project {
  constructor(data) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.initiator = data.initiator;
    this.start_date = data.start_date;
    this.end_date = data.end_date;
    this.status = data.status;
    this.progress_percentage = data.progress_percentage;
    this.tags = data.tags;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Get all projects
  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT dp.*, 
               COUNT(pm.machine_id) as machine_count
        FROM development_projects dp
        LEFT JOIN project_machines pm ON dp.id = pm.project_id
      `;
      
      const conditions = [];
      const params = [];

      if (filters.status) {
        conditions.push('dp.status = ?');
        params.push(filters.status);
      }

      if (filters.search) {
        conditions.push('(dp.title LIKE ? OR dp.description LIKE ? OR dp.initiator LIKE ?)');
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' GROUP BY dp.id ORDER BY dp.created_at DESC';

      const [rows] = await pool.execute(query, params);
      return rows.map(row => new Project(row));
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  }

  // Get project by ID
  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT dp.*, 
                COUNT(pm.machine_id) as machine_count
         FROM development_projects dp
         LEFT JOIN project_machines pm ON dp.id = pm.project_id
         WHERE dp.id = ?
         GROUP BY dp.id`,
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      return new Project(rows[0]);
    } catch (error) {
      console.error('Error fetching project by ID:', error);
      throw error;
    }
  }

  // Get project with associated machines
  static async findByIdWithMachines(id) {
    try {
      const project = await this.findById(id);
      if (!project) {
        return null;
      }

      // Get associated machines
      const [machineRows] = await pool.execute(
        `SELECT m.id, m.machine_number, m.name, m.location, m.status
         FROM machines m
         INNER JOIN project_machines pm ON m.id = pm.machine_id
         WHERE pm.project_id = ?
         ORDER BY m.machine_number`,
        [id]
      );

      project.machines = machineRows;
      return project;
    } catch (error) {
      console.error('Error fetching project with machines:', error);
      throw error;
    }
  }

  // Create new project
  static async create(projectData) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();

      const { title, description, initiator, start_date, end_date, machines = [] } = projectData;
      
      // Validate required fields
      if (!title) {
        throw new Error('Project title is required');
      }

      // Insert project
      const [result] = await connection.execute(
        `INSERT INTO development_projects (title, description, initiator, start_date, end_date, status, progress_percentage) 
         VALUES (?, ?, ?, ?, ?, 'active', 0)`,
        [title, description || null, initiator || null, start_date || null, end_date || null]
      );

      const projectId = result.insertId;

      // Associate machines with project
      if (machines.length > 0) {
        const machineValues = machines.map(machineId => [projectId, parseInt(machineId)]);
        await connection.execute(
          `INSERT INTO project_machines (project_id, machine_id) VALUES ${machineValues.map(() => '(?, ?)').join(', ')}`,
          machineValues.flat()
        );
      }

      await connection.commit();
      return await this.findByIdWithMachines(projectId);
    } catch (error) {
      await connection.rollback();
      console.error('Error creating project:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // Update project
  static async update(id, projectData) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();

      const { title, description, initiator, start_date, end_date, status, progress_percentage, machines } = projectData;
      
      // Update project basic info
      await connection.execute(
        `UPDATE development_projects 
         SET title = ?, description = ?, initiator = ?, start_date = ?, end_date = ?, 
             status = ?, progress_percentage = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [
          title, 
          description || null, 
          initiator || null, 
          start_date || null, 
          end_date || null,
          status || 'active',
          progress_percentage || 0,
          id
        ]
      );

      // Update machine associations if provided
      if (machines !== undefined) {
        // Remove existing associations
        await connection.execute('DELETE FROM project_machines WHERE project_id = ?', [id]);
        
        // Add new associations
        if (machines.length > 0) {
          const machineValues = machines.map(machineId => [id, parseInt(machineId)]);
          await connection.execute(
            `INSERT INTO project_machines (project_id, machine_id) VALUES ${machineValues.map(() => '(?, ?)').join(', ')}`,
            machineValues.flat()
          );
        }
      }

      await connection.commit();
      return await this.findByIdWithMachines(id);
    } catch (error) {
      await connection.rollback();
      console.error('Error updating project:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // Update only progress
  static async updateProgress(id, progressData) {
    try {
      const { progress_percentage, notes } = progressData;
      
      await pool.execute(
        `UPDATE development_projects 
         SET progress_percentage = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [progress_percentage || 0, id]
      );

      // If notes provided, you might want to store them in a separate notes/comments table
      // For now, we'll just update the project
      
      return await this.findById(id);
    } catch (error) {
      console.error('Error updating project progress:', error);
      throw error;
    }
  }

  // Delete project
  static async delete(id) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();

      // Delete machine associations first (due to foreign key constraints)
      await connection.execute('DELETE FROM project_machines WHERE project_id = ?', [id]);
      
      // Delete project
      await connection.execute('DELETE FROM development_projects WHERE id = ?', [id]);

      await connection.commit();
      return true;
    } catch (error) {
      await connection.rollback();
      console.error('Error deleting project:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // Get project statistics
  static async getStatistics() {
    try {
      const [stats] = await pool.execute(`
        SELECT 
          COUNT(*) as total_projects,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_projects,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_projects,
          AVG(progress_percentage) as average_progress
        FROM development_projects
      `);

      return stats[0];
    } catch (error) {
      console.error('Error fetching project statistics:', error);
      throw error;
    }
  }
}

export default Project;
