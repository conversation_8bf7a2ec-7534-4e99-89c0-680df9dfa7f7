import { Hono } from 'hono';
import { Machine } from '../../models/Machine.js';

export const machineApiRoutes = new Hono();

// GET /api/machines - Get all machines
machineApiRoutes.get('/', async c => {
  try {
    const machines = await Machine.findAll();
    return c.json(machines);
  } catch (error) {
    console.error('Error fetching machines:', error);
    return c.json({ error: 'Failed to fetch machines' }, 500);
  }
});

// GET /api/machines/:id - Get machine by ID
machineApiRoutes.get('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.findById(id);

    if (!machine) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    return c.json(machine);
  } catch (error) {
    console.error('Error fetching machine:', error);
    return c.json({ error: 'Failed to fetch machine' }, 500);
  }
});

// POST /api/machines - Create new machine
machineApiRoutes.post('/', async c => {
  try {
    const body = await c.req.json();

    // Validate required fields
    if (!body.machine_number || !body.name) {
      return c.json(
        {
          error: 'Missing required fields: machine_number and name are required',
        },
        400
      );
    }

    const machine = await Machine.create(body);

    return c.json(
      {
        ...machine,
        qr_code_generated: true,
      },
      201
    );
  } catch (error) {
    console.error('Error creating machine:', error);

    // Handle duplicate machine number
    if (error.message.includes('Duplicate entry')) {
      return c.json(
        {
          error: 'Machine number already exists',
        },
        409
      );
    }

    return c.json({ error: 'Failed to create machine' }, 500);
  }
});

// PUT /api/machines/:id - Update machine
machineApiRoutes.put('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();

    const machine = await Machine.update(id, body);

    if (!machine) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    return c.json(machine);
  } catch (error) {
    console.error('Error updating machine:', error);
    return c.json({ error: 'Failed to update machine' }, 500);
  }
});

// DELETE /api/machines/:id - Delete machine
machineApiRoutes.delete('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await Machine.delete(id);

    if (!deleted) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    return c.json({ message: 'Machine deleted successfully' });
  } catch (error) {
    console.error('Error deleting machine:', error);
    return c.json({ error: 'Failed to delete machine' }, 500);
  }
});

// POST /api/machines/:id/regenerate-qr - Regenerate QR code for a machine
machineApiRoutes.post('/:id/regenerate-qr', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const machine = await Machine.regenerateQRCode(id);

    if (!machine) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    return c.json({
      message: 'QR code regenerated successfully',
      machine_number: machine.machine_number,
      machine_id: machine.id,
    });
  } catch (error) {
    console.error('Error regenerating QR code:', error);
    return c.json({ error: 'Failed to regenerate QR code' }, 500);
  }
});

// POST /api/machines/regenerate-all-qr - Regenerate QR codes for all machines
machineApiRoutes.post('/regenerate-all-qr', async c => {
  try {
    const results = await Machine.regenerateAllQRCodes();

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    return c.json({
      message: `QR codes regenerated: ${successful} successful, ${failed} failed`,
      results: results,
    });
  } catch (error) {
    console.error('Error regenerating all QR codes:', error);
    return c.json({ error: 'Failed to regenerate QR codes' }, 500);
  }
});
