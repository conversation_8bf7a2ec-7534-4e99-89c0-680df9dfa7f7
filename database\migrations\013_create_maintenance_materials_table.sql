-- Create maintenance_materials table for tracking parts and materials used in maintenance
CREATE TABLE IF NOT EXISTS maintenance_materials (
  id INT AUTO_INCREMENT PRIMARY KEY,
  maintenance_request_id INT NOT NULL,
  material_type ENUM('stock_part', 'external_material') NOT NULL DEFAULT 'stock_part',

  -- For stock parts (from parts table)
  part_id INT NULL,

  -- For external materials (not in stock)
  material_name VARCHAR(255) NULL,
  material_description TEXT NULL,
  supplier VARCHAR(255) NULL,

  -- Common fields
  quantity DECIMAL(10,3) NOT NULL DEFAULT 1,
  unit_of_measure VARCHAR(50) DEFAULT 'tk',
  unit_cost DECIMAL(10,2) DEFAULT 0,
  total_cost DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,

  -- Status and notes
  status ENUM('planned', 'ordered', 'received', 'used') NOT NULL DEFAULT 'planned',
  notes TEXT,

  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign keys
  FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE CASCADE,
  FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE SET NULL,

  -- Indexes
  INDEX idx_maintenance_request_id (maintenance_request_id),
  INDEX idx_part_id (part_id),
  INDEX idx_material_type (material_type),
  INDEX idx_status (status)

  -- Note: CHECK constraints with NULL checks not supported in this MariaDB version
  -- Data validation will be handled at application level
);

-- Create index for cost calculations
CREATE INDEX idx_maintenance_materials_cost ON maintenance_materials(total_cost);

-- Create view for easy material summary per maintenance request
CREATE VIEW maintenance_materials_summary AS
SELECT
  mm.maintenance_request_id,
  COUNT(*) as total_materials,
  SUM(CASE WHEN mm.material_type = 'stock_part' THEN 1 ELSE 0 END) as stock_parts_count,
  SUM(CASE WHEN mm.material_type = 'external_material' THEN 1 ELSE 0 END) as external_materials_count,
  SUM(mm.total_cost) as total_materials_cost,
  SUM(CASE WHEN mm.material_type = 'stock_part' THEN mm.total_cost ELSE 0 END) as stock_parts_cost,
  SUM(CASE WHEN mm.material_type = 'external_material' THEN mm.total_cost ELSE 0 END) as external_materials_cost
FROM maintenance_materials mm
GROUP BY mm.maintenance_request_id;
