-- Add partner support to issues table
ALTER TABLE issues 
ADD COLUMN partner_id INT NULL AFTER assigned_to,
ADD COLUMN partner_notes TEXT NULL AFTER partner_id,
ADD COLUMN partner_cost DECIMAL(10,2) NULL AFTER partner_notes,
ADD FOREIGN KEY fk_issue_partner (partner_id) REFERENCES maintenance_partners(id) ON DELETE SET NULL;

-- Create index for partner_id
CREATE INDEX idx_issue_partner_id ON issues(partner_id);

-- Update some existing issues to have partners for testing
UPDATE issues 
SET partner_id = 2, partner_notes = 'Määratud Elektripartner AS-le elektrisüsteemi rikke lahendamiseks'
WHERE id IN (SELECT id FROM (SELECT id FROM issues WHERE issue_type = 'electrical' AND status = 'open' LIMIT 2) as temp);

UPDATE issues 
SET partner_id = 3, partner_notes = 'Mehhaanilise rikke kiire lahendamine vajalik'
WHERE id IN (SELECT id FROM (SELECT id FROM issues WHERE issue_type = 'mechanical' AND priority = 'high' LIMIT 1) as temp);

UPDATE issues 
SET partner_id = 5, partner_notes = 'Hädahooldus - kriitilise rikke lahendamine'
WHERE id IN (SELECT id FROM (SELECT id FROM issues WHERE priority = 'critical' LIMIT 1) as temp);
