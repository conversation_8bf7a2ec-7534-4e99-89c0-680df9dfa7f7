@echo off
setlocal enabledelayedexpansion

echo 🚀 Setting up CMMS development environment...

REM Check if Bun is installed
bun --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Bun is not installed. Please install Bun first:
    echo    Visit https://bun.sh/ and follow installation instructions
    echo    Or run: powershell -c "irm bun.sh/install.ps1 | iex"
    pause
    exit /b 1
) else (
    echo ✅ Bun is already installed
)

REM Install dependencies
echo 📦 Installing dependencies...
bun install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Setup environment file
if not exist .env (
    echo ⚙️ Creating .env file from template...
    copy .env.example .env
    echo ✅ .env file created. Please update it with your database credentials.
) else (
    echo ✅ .env file already exists
)

REM Check if MySQL is available
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ MySQL/MariaDB client not found. Please install MariaDB or MySQL
    echo    You can install MariaDB using: winget install MariaDB.Server
) else (
    echo ✅ MySQL/MariaDB client found
    
    REM Try to connect to database
    mysql -h localhost -u root -e "SELECT 1;" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Database connection successful
        
        REM Run migrations
        echo 🔄 Running database migrations...
        bun run migrate
        if %errorlevel% equ 0 (
            echo ✅ Database migrations completed
        ) else (
            echo ❌ Database migration failed
        )
    ) else (
        echo ⚠️ Cannot connect to database. Please:
        echo    1. Start your MariaDB/MySQL server
        echo    2. Update .env with correct database credentials
        echo    3. Run 'bun run migrate' to create tables
    )
)

REM Install Playwright browsers
echo 🎭 Installing Playwright browsers...
bunx playwright install chromium

echo.
echo 🎉 Setup complete!
echo.
echo Next steps:
echo 1. Update .env with your database credentials
echo 2. Start the development server: bun run dev
echo 3. Open http://localhost:8080 in your browser
echo.
echo Available commands:
echo   bun run dev        - Start development server
echo   bun run test       - Run API tests
echo   bun run test:e2e   - Run E2E tests
echo   bun run migrate    - Run database migrations
echo.
pause
