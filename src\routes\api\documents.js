import { Hono } from 'hono';
import Document from '../../models/Document.js';
import multer from 'multer';

const app = new Hono();

// Multer configuration for file uploads (memory storage)
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: Document.getMaxFileSize() // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedMimeTypes = Document.getAllowedMimeTypes();
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Failitüüp ${file.mimetype} ei ole lubatud`), false);
    }
  }
});

// GET /api/documents - Kõigi dokumentide leidmine
app.get('/', async (c) => {
  try {
    const filters = {
      machine_id: c.req.query('machine_id'),
      document_type: c.req.query('document_type'),
      search: c.req.query('search'),
      sort_by: c.req.query('sort_by'),
      sort_order: c.req.query('sort_order'),
      limit: c.req.query('limit')
    };

    const documents = await Document.findAll(filters);

    // Eemaldame file_data välja response'ist (liiga suur)
    const documentsWithoutFileData = documents.map(doc => {
      const { file_data, ...docWithoutData } = doc;
      return docWithoutData;
    });

    return c.json({
      success: true,
      data: documentsWithoutFileData,
      count: documentsWithoutFileData.length
    });
  } catch (error) {
    console.error('Error fetching documents:', error);
    return c.json({
      success: false,
      error: 'Viga dokumentide laadimisel'
    }, 500);
  }
});

// GET /api/documents/statistics - Dokumentide statistika
app.get('/statistics', async (c) => {
  try {
    const stats = await Document.getStatistics();
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching document statistics:', error);
    return c.json({
      success: false,
      error: 'Viga statistika laadimisel'
    }, 500);
  }
});

// GET /api/documents/types - Dokumendi tüüpide nimekiri
app.get('/types', async (c) => {
  try {
    const types = Document.getDocumentTypes();
    return c.json({
      success: true,
      data: types
    });
  } catch (error) {
    console.error('Error fetching document types:', error);
    return c.json({
      success: false,
      error: 'Viga dokumendi tüüpide laadimisel'
    }, 500);
  }
});

// GET /api/documents/machine/:machineId - Masina dokumentide leidmine
app.get('/machine/:machineId', async (c) => {
  try {
    const documents = await Document.findByMachine(c.req.param('machineId'));

    // Eemaldame file_data välja response'ist
    const documentsWithoutFileData = documents.map(doc => {
      const { file_data, ...docWithoutData } = doc;
      return docWithoutData;
    });

    return c.json({
      success: true,
      data: documentsWithoutFileData,
      count: documentsWithoutFileData.length
    });
  } catch (error) {
    console.error('Error fetching machine documents:', error);
    return c.json({
      success: false,
      error: 'Viga masina dokumentide laadimisel'
    }, 500);
  }
});

// GET /api/documents/:id - Ühe dokumendi leidmine (ilma file_data'ta)
app.get('/:id', async (c) => {
  try {
    const document = await Document.findById(c.req.param('id'));
    if (!document) {
      return c.json({
        success: false,
        error: 'Dokument ei leitud'
      }, 404);
    }

    // Eemaldame file_data välja response'ist
    const { file_data, ...docWithoutData } = document;

    return c.json({
      success: true,
      data: docWithoutData
    });
  } catch (error) {
    console.error('Error fetching document:', error);
    return c.json({
      success: false,
      error: 'Viga dokumendi laadimisel'
    }, 500);
  }
});

// GET /api/documents/:id/download - Dokumendi allalaadimine
app.get('/:id/download', async (c) => {
  try {
    const fileData = await Document.getFileData(c.req.param('id'));
    if (!fileData) {
      return c.json({
        success: false,
        error: 'Dokument ei leitud'
      }, 404);
    }

    // Logime ligipääsu
    await Document.logAccess(c.req.param('id'), 'download', {
      user_identifier: 'api_user',
      user_agent: c.req.header('user-agent'),
      ip_address: c.req.header('x-forwarded-for') || c.req.header('x-real-ip')
    });

    // Seadistame response header'id
    c.header('Content-Type', fileData.mime_type);
    c.header('Content-Disposition', `attachment; filename="${fileData.file_name}"`);
    c.header('Content-Length', fileData.file_size.toString());

    return c.body(fileData.file_data);
  } catch (error) {
    console.error('Error downloading document:', error);
    return c.json({
      success: false,
      error: 'Viga dokumendi allalaadimisel'
    }, 500);
  }
});

// POST /api/documents - Uue dokumendi üleslaadimine
app.post('/', async (c) => {
  try {
    // Hono doesn't have built-in multipart support, so we'll handle this differently
    // For now, we'll expect JSON with base64 encoded file data
    const body = await c.req.json();

    const requiredFields = ['machine_id', 'title', 'document_type', 'file_name', 'file_data', 'mime_type'];
    const missingFields = requiredFields.filter(field => !body[field]);

    if (missingFields.length > 0) {
      return c.json({
        success: false,
        error: `Puuduvad kohustuslikud väljad: ${missingFields.join(', ')}`
      }, 400);
    }

    // Decode base64 file data
    let fileBuffer;
    try {
      fileBuffer = Buffer.from(body.file_data, 'base64');
    } catch (error) {
      return c.json({
        success: false,
        error: 'Vigane faili andmete formaat'
      }, 400);
    }

    // Kontrolli faili suurust
    if (fileBuffer.length > Document.getMaxFileSize()) {
      return c.json({
        success: false,
        error: 'Fail on liiga suur (max 10MB)'
      }, 400);
    }

    // Kontrolli MIME tüüpi
    const allowedMimeTypes = Document.getAllowedMimeTypes();
    if (!allowedMimeTypes.includes(body.mime_type)) {
      return c.json({
        success: false,
        error: 'Failitüüp ei ole lubatud'
      }, 400);
    }

    // Määra faili laiend
    const fileExtension = body.file_name.split('.').pop().toLowerCase();

    const documentData = {
      machine_id: parseInt(body.machine_id),
      title: body.title,
      description: body.description || null,
      document_type: body.document_type,
      file_name: body.file_name,
      file_data: fileBuffer,
      file_size: fileBuffer.length,
      mime_type: body.mime_type,
      file_extension: fileExtension,
      uploaded_by: body.uploaded_by || 'api_user',
      version: body.version || '1.0'
    };

    const document = await Document.create(documentData);

    // Logime üleslaadimise
    await Document.logAccess(document.id, 'upload', {
      user_identifier: documentData.uploaded_by,
      user_agent: c.req.header('user-agent'),
      ip_address: c.req.header('x-forwarded-for') || c.req.header('x-real-ip')
    });

    // Tagastame dokumendi ilma file_data'ta
    const { file_data, ...docWithoutData } = document;

    return c.json({
      success: true,
      data: docWithoutData,
      message: 'Dokument edukalt üles laaditud'
    }, 201);
  } catch (error) {
    console.error('Error uploading document:', error);
    return c.json({
      success: false,
      error: 'Viga dokumendi üleslaadimisel'
    }, 500);
  }
});

// POST /api/documents/upload - Multipart file upload
app.post('/upload', async (c) => {
  try {
    const formData = await c.req.formData();

    const title = formData.get('title');
    const description = formData.get('description');
    const document_type = formData.get('document_type');
    const machine_id = formData.get('machine_id');
    const project_id = formData.get('project_id');
    const file = formData.get('file');

    // Validate required fields
    if (!title || !document_type || !file) {
      return c.json({
        success: false,
        error: 'Puuduvad kohustuslikud väljad: title, document_type, file'
      }, 400);
    }

    // Validate that either machine_id or project_id is provided
    if (!machine_id && !project_id) {
      return c.json({
        success: false,
        error: 'Dokument peab olema seotud masina või projektiga'
      }, 400);
    }

    // Convert file to buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // Check file size
    if (fileBuffer.length > Document.getMaxFileSize()) {
      return c.json({
        success: false,
        error: 'Fail on liiga suur (max 10MB)'
      }, 400);
    }

    // Check MIME type
    const allowedMimeTypes = Document.getAllowedMimeTypes();
    if (!allowedMimeTypes.includes(file.type)) {
      return c.json({
        success: false,
        error: 'Failitüüp ei ole lubatud'
      }, 400);
    }

    // Get file extension
    const fileExtension = file.name.split('.').pop().toLowerCase();

    const documentData = {
      machine_id: machine_id ? parseInt(machine_id) : null,
      project_id: project_id ? parseInt(project_id) : null,
      title: title,
      description: description || null,
      document_type: document_type,
      file_name: file.name,
      file_data: fileBuffer,
      file_size: fileBuffer.length,
      mime_type: file.type,
      file_extension: fileExtension,
      uploaded_by: 'web_user',
      version: '1.0'
    };

    const document = await Document.create(documentData);

    // Log upload
    await Document.logAccess(document.id, 'upload', {
      user_identifier: 'web_user',
      user_agent: c.req.header('user-agent'),
      ip_address: c.req.header('x-forwarded-for') || c.req.header('x-real-ip')
    });

    // Return document without file_data
    const { file_data, ...docWithoutData } = document;

    return c.json({
      success: true,
      data: docWithoutData,
      message: 'Dokument edukalt üles laaditud'
    }, 201);
  } catch (error) {
    console.error('Error uploading document via multipart:', error);
    return c.json({
      success: false,
      error: 'Viga dokumendi üleslaadimisel'
    }, 500);
  }
});

// PUT /api/documents/:id - Dokumendi uuendamine
app.put('/:id', async (c) => {
  try {
    const body = await c.req.json();
    const document = await Document.findById(c.req.param('id'));

    if (!document) {
      return c.json({
        success: false,
        error: 'Dokument ei leitud'
      }, 404);
    }

    const updatedDocument = await Document.update(c.req.param('id'), body);

    // Tagastame dokumendi ilma file_data'ta
    const { file_data, ...docWithoutData } = updatedDocument;

    return c.json({
      success: true,
      data: docWithoutData,
      message: 'Dokument edukalt uuendatud'
    });
  } catch (error) {
    console.error('Error updating document:', error);
    return c.json({
      success: false,
      error: 'Viga dokumendi uuendamisel'
    }, 500);
  }
});

// DELETE /api/documents/:id - Dokumendi kustutamine (soft delete)
app.delete('/:id', async (c) => {
  try {
    const document = await Document.findById(c.req.param('id'));
    if (!document) {
      return c.json({
        success: false,
        error: 'Dokument ei leitud'
      }, 404);
    }

    await Document.delete(c.req.param('id'));

    // Logime kustutamise
    await Document.logAccess(c.req.param('id'), 'delete', {
      user_identifier: 'api_user',
      user_agent: c.req.header('user-agent'),
      ip_address: c.req.header('x-forwarded-for') || c.req.header('x-real-ip')
    });

    return c.json({
      success: true,
      message: 'Dokument edukalt kustutatud'
    });
  } catch (error) {
    console.error('Error deleting document:', error);
    return c.json({
      success: false,
      error: 'Viga dokumendi kustutamisel'
    }, 500);
  }
});

export { app as documentsApiRoutes };
