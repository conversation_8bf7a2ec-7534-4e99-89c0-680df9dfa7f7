import { Hono } from 'hono';
import Part from '../../models/Part.js';
import MachinePart from '../../models/MachinePart.js';
import { Machine } from '../../models/Machine.js';

const app = new Hono();

// GET /api/parts - <PERSON><PERSON><PERSON> varuosade leidmine
app.get('/', async (c) => {
  try {
    const filters = {
      category: c.req.query('category'),
      is_active: c.req.query('is_active') !== undefined ? c.req.query('is_active') === 'true' : true, // Default to active only
      low_stock: c.req.query('low_stock') === 'true',
      search: c.req.query('search'),
      sort_by: c.req.query('sort_by'),
      sort_order: c.req.query('sort_order'),
      limit: c.req.query('limit')
    };

    const machineId = c.req.query('machine_id');

    let parts;
    if (machineId) {
      // If machine_id is specified, get parts for that machine
      const machineParts = await MachinePart.findByMachine(machineId);
      parts = machineParts.map(mp => ({
        id: mp.part_id,
        name: mp.part_name,
        part_number: mp.part_number,
        category: mp.part_category,
        quantity_in_stock: mp.quantity_in_stock,
        minimum_stock_level: mp.minimum_stock_level,
        unit_price: mp.unit_price,
        location: mp.location,
        is_critical: mp.is_critical,
        quantity_needed: mp.quantity_needed,
        stock_status: mp.stock_status
      }));
    } else {
      parts = await Part.findAll(filters);
    }

    // Lisame igale varuosale masinate andmed (kui ei ole juba machine-specific)
    const partsWithMachines = await Promise.all(
      parts.map(async (part) => {
        const machines = machineId ? [] : await MachinePart.findByPart(part.id);
        return {
          ...part,
          machines: machines
        };
      })
    );

    return c.json({
      success: true,
      data: partsWithMachines,
      count: partsWithMachines.length
    });
  } catch (error) {
    console.error('Error fetching parts:', error);
    return c.json({
      success: false,
      error: 'Viga varuosade laadimisel'
    }, 500);
  }
});

// GET /api/parts/categories - Kategooriate leidmine
app.get('/categories', async (c) => {
  try {
    const categories = await Part.getCategories();
    return c.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return c.json({
      success: false,
      error: 'Viga kategooriate laadimisel'
    }, 500);
  }
});

// GET /api/parts/low-stock - Madala varuga varuosad
app.get('/low-stock', async (c) => {
  try {
    const parts = await Part.findLowStock();
    return c.json({
      success: true,
      data: parts,
      count: parts.length
    });
  } catch (error) {
    console.error('Error fetching low stock parts:', error);
    return c.json({
      success: false,
      error: 'Viga madala varuga varuosade laadimisel'
    }, 500);
  }
});

// GET /api/parts/statistics - Varuosade statistika
app.get('/statistics', async (c) => {
  try {
    const stats = await Part.getStatistics();
    const machinePartsStats = await MachinePart.getStatistics();

    return c.json({
      success: true,
      data: {
        ...stats,
        ...machinePartsStats
      }
    });
  } catch (error) {
    console.error('Error fetching parts statistics:', error);
    return c.json({
      success: false,
      error: 'Viga statistika laadimisel'
    }, 500);
  }
});

// Machine-Parts endpoints (need to be before /:id route)

// GET /api/parts/machines - Kõigi masinate nimekiri
app.get('/machines', async (c) => {
  try {
    const machines = await Machine.findAll({ is_active: true });
    return c.json({
      success: true,
      data: machines.map(machine => ({
        id: machine.id,
        name: machine.name,
        machine_number: machine.machine_number,
        location: machine.location
      }))
    });
  } catch (error) {
    console.error('Error fetching machines:', error);
    return c.json({
      success: false,
      error: 'Viga masinate laadimisel'
    }, 500);
  }
});

// GET /api/parts/archived - Arhiveeritud varuosade leidmine
app.get('/archived', async (c) => {
  try {
    const filters = {
      category: c.req.query('category'),
      search: c.req.query('search'),
      sort_by: c.req.query('sort_by'),
      sort_order: c.req.query('sort_order'),
      limit: c.req.query('limit')
    };

    const archivedParts = await Part.findArchived(filters);

    // Lisame igale varuosale masinate andmed
    const partsWithMachines = await Promise.all(
      archivedParts.map(async (part) => {
        const machines = await MachinePart.findByPart(part.id);
        return {
          ...part,
          machines: machines
        };
      })
    );

    return c.json({
      success: true,
      data: partsWithMachines,
      count: partsWithMachines.length
    });
  } catch (error) {
    console.error('Error fetching archived parts:', error);
    return c.json({
      success: false,
      error: 'Viga arhiveeritud varuosade laadimisel'
    }, 500);
  }
});

// GET /api/parts/:id - Ühe varuosa leidmine
app.get('/:id', async (c) => {
  try {
    const part = await Part.findById(c.req.param('id'));
    if (!part) {
      return c.json({
        success: false,
        error: 'Varuosa ei leitud'
      }, 404);
    }

    // Leiame ka masinate seosed
    const machines = await MachinePart.findByPart(c.req.param('id'));

    return c.json({
      success: true,
      data: {
        ...part,
        machines: machines
      }
    });
  } catch (error) {
    console.error('Error fetching part:', error);
    return c.json({
      success: false,
      error: 'Viga varuosa laadimisel'
    }, 500);
  }
});

// POST /api/parts - Uue varuosa loomine
app.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const requiredFields = ['part_number', 'name', 'category'];
    const missingFields = requiredFields.filter(field => !body[field]);

    if (missingFields.length > 0) {
      return c.json({
        success: false,
        error: `Puuduvad kohustuslikud väljad: ${missingFields.join(', ')}`
      }, 400);
    }

    const part = await Part.create(body);
    return c.json({
      success: true,
      data: part,
      message: 'Varuosa edukalt lisatud'
    }, 201);
  } catch (error) {
    console.error('Error creating part:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      return c.json({
        success: false,
        error: 'Varuosa number on juba kasutusel'
      }, 400);
    } else {
      return c.json({
        success: false,
        error: 'Viga varuosa loomisel'
      }, 500);
    }
  }
});

// PUT /api/parts/:id - Varuosa uuendamine
app.put('/:id', async (c) => {
  try {
    const body = await c.req.json();
    const part = await Part.findById(c.req.param('id'));
    if (!part) {
      return c.json({
        success: false,
        error: 'Varuosa ei leitud'
      }, 404);
    }

    const updatedPart = await Part.update(c.req.param('id'), body);
    return c.json({
      success: true,
      data: updatedPart,
      message: 'Varuosa edukalt uuendatud'
    });
  } catch (error) {
    console.error('Error updating part:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      return c.json({
        success: false,
        error: 'Varuosa number on juba kasutusel'
      }, 400);
    } else {
      return c.json({
        success: false,
        error: 'Viga varuosa uuendamisel'
      }, 500);
    }
  }
});

// POST /api/parts/:id/restore - Varuosa taastamine arhiivist
app.post('/:id/restore', async (c) => {
  try {
    // Kontrollime, kas varuosa on arhiveeritud
    const archivedParts = await Part.findArchived();
    const archivedPart = archivedParts.find(p => p.id === parseInt(c.req.param('id')));

    if (!archivedPart) {
      return c.json({
        success: false,
        error: 'Arhiveeritud varuosa ei leitud'
      }, 404);
    }

    const restoredPart = await Part.restore(c.req.param('id'));
    return c.json({
      success: true,
      data: restoredPart,
      message: 'Varuosa edukalt taastatud arhiivist'
    });
  } catch (error) {
    console.error('Error restoring part:', error);
    return c.json({
      success: false,
      error: 'Viga varuosa taastamisel'
    }, 500);
  }
});

// PUT /api/parts/:id/stock - Laokoguse uuendamine
app.put('/:id/stock', async (c) => {
  try {
    const body = await c.req.json();
    const { quantity, reason } = body;

    if (quantity === undefined || quantity < 0) {
      return c.json({
        success: false,
        error: 'Kogus peab olema positiivne number'
      }, 400);
    }

    const part = await Part.findById(c.req.param('id'));
    if (!part) {
      return c.json({
        success: false,
        error: 'Varuosa ei leitud'
      }, 404);
    }

    const updatedPart = await Part.updateStock(c.req.param('id'), quantity, reason);
    return c.json({
      success: true,
      data: updatedPart,
      message: 'Laokogus edukalt uuendatud'
    });
  } catch (error) {
    console.error('Error updating stock:', error);
    return c.json({
      success: false,
      error: 'Viga laokoguse uuendamisel'
    }, 500);
  }
});

// DELETE /api/parts/:id - Varuosa kustutamine (soft delete - arhiveerimine)
app.delete('/:id', async (c) => {
  try {
    const part = await Part.findById(c.req.param('id'));
    if (!part) {
      return c.json({
        success: false,
        error: 'Varuosa ei leitud'
      }, 404);
    }

    await Part.delete(c.req.param('id'));
    return c.json({
      success: true,
      message: 'Varuosa edukalt arhiveeritud'
    });
  } catch (error) {
    console.error('Error archiving part:', error);
    return c.json({
      success: false,
      error: 'Viga varuosa arhiveerimisel'
    }, 500);
  }
});



// GET /api/parts/machine/:machineId - Masina varuosad
app.get('/machine/:machineId', async (c) => {
  try {
    const machineParts = await MachinePart.findByMachine(c.req.param('machineId'));
    return c.json({
      success: true,
      data: machineParts,
      count: machineParts.length
    });
  } catch (error) {
    console.error('Error fetching machine parts:', error);
    return c.json({
      success: false,
      error: 'Viga masina varuosade laadimisel'
    }, 500);
  }
});

// POST /api/parts/machine-parts - Masina-varuosa seose loomine
app.post('/machine-parts', async (c) => {
  try {
    const body = await c.req.json();
    const requiredFields = ['machine_id', 'part_id'];
    const missingFields = requiredFields.filter(field => !body[field]);

    if (missingFields.length > 0) {
      return c.json({
        success: false,
        error: `Puuduvad kohustuslikud väljad: ${missingFields.join(', ')}`
      }, 400);
    }

    const machinePart = await MachinePart.create(body);
    return c.json({
      success: true,
      data: machinePart,
      message: 'Masina-varuosa seos edukalt loodud'
    }, 201);
  } catch (error) {
    console.error('Error creating machine part:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      return c.json({
        success: false,
        error: 'See varuosa on juba seostatud selle masinaga'
      }, 400);
    } else {
      return c.json({
        success: false,
        error: 'Viga seose loomisel'
      }, 500);
    }
  }
});

// DELETE /api/parts/machine-parts/:id - Masina-varuosa seose kustutamine
app.delete('/machine-parts/:id', async (c) => {
  try {
    const machinePart = await MachinePart.findById(c.req.param('id'));
    if (!machinePart) {
      return c.json({
        success: false,
        error: 'Masina-varuosa seos ei leitud'
      }, 404);
    }

    await MachinePart.delete(c.req.param('id'));
    return c.json({
      success: true,
      message: 'Seos edukalt kustutatud'
    });
  } catch (error) {
    console.error('Error deleting machine part:', error);
    return c.json({
      success: false,
      error: 'Viga seose kustutamisel'
    }, 500);
  }
});

export { app as partsApiRoutes };
