import QRCode from 'qrcode';

export async function generateQRCode(machineNumber) {
  try {
    const baseUrl = process.env.BASE_URL || 'http://localhost:8080';
    const operatorUrl = `${baseUrl}/operator/${machineNumber}`;
    
    const qrCodeBuffer = await QRCode.toBuffer(operatorUrl, {
      type: 'png',
      width: parseInt(process.env.QR_CODE_SIZE) || 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    return qrCodeBuffer;
  } catch (error) {
    throw new Error(`Failed to generate QR code: ${error.message}`);
  }
}

export async function generateQRCodeDataURL(machineNumber) {
  try {
    const baseUrl = process.env.BASE_URL || 'http://localhost:8080';
    const operatorUrl = `${baseUrl}/operator/${machineNumber}`;
    
    const qrCodeDataURL = await QRCode.toDataURL(operatorUrl, {
      width: parseInt(process.env.QR_CODE_SIZE) || 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    return qrCodeDataURL;
  } catch (error) {
    throw new Error(`Failed to generate QR code data URL: ${error.message}`);
  }
}
