import { Machine } from './Machine.js';
import { MachineGroup } from './MachineGroup.js';
import { Document } from './Document.js';
import { MachinePart } from './MachinePart.js';
import { MaintenanceRequest } from './MaintenanceRequest.js';
import { Issue } from './Issue.js';
import { Part } from './Part.js';
import { Project } from './Project.js';
import User from './User.js';
import Session from './Session.js';
import LoginAttempt from './LoginAttempt.js';

// Define associations
User.hasMany(Session, { foreignKey: 'user_id', as: 'sessions' });
Session.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export {
  Machine,
  MachineGroup,
  Document,
  MachinePart,
  MaintenanceRequest,
  Issue,
  Part,
  Project,
  User,
  Session,
  LoginAttempt
};
