import { describe, it, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../server.js';

describe('Partners API', () => {
  let testPartnerId;

  beforeAll(async () => {
    // Clean up any existing test partners
    try {
      const response = await app.request('/api/partners');
      const partners = await response.json();

      for (const partner of partners) {
        if (partner.email && partner.email.includes('test')) {
          await app.request(`/api/partners/${partner.id}`, { method: 'DELETE' });
        }
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  afterAll(async () => {
    // Clean up test partner
    if (testPartnerId) {
      try {
        await app.request(`/api/partners/${testPartnerId}`, { method: 'DELETE' });
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe('POST /api/partners', () => {
    it('should create a new maintenance partner', async () => {
      const partnerData = {
        company_name: 'Test Partner OÜ',
        contact_person: 'Test Kontakt',
        email: '<EMAIL>',
        phone: '+372 5000 0000',
        address: 'Test aadress 123',
        specializations: ['mechanical', 'electrical'],
        hourly_rate: 50.0,
        notes: 'Test partner for unit tests',
      };

      const response = await app.request('/api/partners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(partnerData),
      });

      expect(response.status).toBe(201);

      const partner = await response.json();
      testPartnerId = partner.id;

      expect(partner.company_name).toBe(partnerData.company_name);
      expect(partner.email).toBe(partnerData.email);
      expect(partner.specializations).toEqual(partnerData.specializations);
      expect(parseFloat(partner.hourly_rate)).toBe(partnerData.hourly_rate);
      expect(partner.is_active).toBe(1); // MariaDB returns 1 for true
    });

    it('should reject partner with missing required fields', async () => {
      const response = await app.request('/api/partners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ company_name: 'Test' }), // Missing email
      });

      expect(response.status).toBe(400);

      const error = await response.json();
      expect(error.error).toContain('Missing required fields');
    });

    it('should reject partner with duplicate email', async () => {
      const partnerData = {
        company_name: 'Another Test Partner',
        email: '<EMAIL>', // Same email as first test
      };

      const response = await app.request('/api/partners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(partnerData),
      });

      expect(response.status).toBe(400);

      const error = await response.json();
      expect(error.error).toContain('already exists');
    });
  });

  describe('GET /api/partners', () => {
    it('should get all maintenance partners', async () => {
      const response = await app.request('/api/partners');

      expect(response.status).toBe(200);

      const partners = await response.json();
      expect(Array.isArray(partners)).toBe(true);
      expect(partners.length).toBeGreaterThan(0);

      // Check that our test partner is included
      const testPartner = partners.find(p => p.email === '<EMAIL>');
      expect(testPartner).toBeDefined();
    });

    it('should filter partners by active status', async () => {
      const response = await app.request('/api/partners?is_active=true');

      expect(response.status).toBe(200);

      const partners = await response.json();
      expect(Array.isArray(partners)).toBe(true);

      // All returned partners should be active
      partners.forEach(partner => {
        expect(partner.is_active).toBe(1); // MariaDB returns 1 for true
      });
    });

    it('should filter partners by specialization', async () => {
      const response = await app.request('/api/partners?specialization=mechanical');

      expect(response.status).toBe(200);

      const partners = await response.json();
      expect(Array.isArray(partners)).toBe(true);

      // All returned partners should have mechanical specialization
      partners.forEach(partner => {
        expect(partner.specializations).toContain('mechanical');
      });
    });

    it('should search partners by company name', async () => {
      const response = await app.request('/api/partners?search=Test Partner');

      expect(response.status).toBe(200);

      const partners = await response.json();
      expect(Array.isArray(partners)).toBe(true);

      // Should find our test partner
      const testPartner = partners.find(p => p.company_name === 'Test Partner OÜ');
      expect(testPartner).toBeDefined();
    });
  });

  describe('GET /api/partners/:id', () => {
    it('should get partner by ID', async () => {
      const response = await app.request(`/api/partners/${testPartnerId}`);

      expect(response.status).toBe(200);

      const partner = await response.json();
      expect(partner.id).toBe(testPartnerId);
      expect(partner.company_name).toBe('Test Partner OÜ');
      expect(partner.email).toBe('<EMAIL>');
    });

    it('should return 404 for non-existent partner', async () => {
      const response = await app.request('/api/partners/99999');

      expect(response.status).toBe(404);
    });
  });

  describe('PUT /api/partners/:id', () => {
    it('should update partner information', async () => {
      const updateData = {
        company_name: 'Updated Test Partner OÜ',
        hourly_rate: 60.0,
        notes: 'Updated notes',
      };

      const response = await app.request(`/api/partners/${testPartnerId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData),
      });

      expect(response.status).toBe(200);

      const partner = await response.json();
      expect(partner.company_name).toBe(updateData.company_name);
      expect(parseFloat(partner.hourly_rate)).toBe(updateData.hourly_rate);
      expect(partner.notes).toBe(updateData.notes);
    });

    it('should return 404 for non-existent partner', async () => {
      const response = await app.request('/api/partners/99999', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ company_name: 'Test' }),
      });

      expect(response.status).toBe(404);
    });
  });

  describe('POST /api/partners/:id/activate', () => {
    it('should activate partner', async () => {
      // First deactivate
      await app.request(`/api/partners/${testPartnerId}/deactivate`, { method: 'POST' });

      // Then activate
      const response = await app.request(`/api/partners/${testPartnerId}/activate`, {
        method: 'POST',
      });

      expect(response.status).toBe(200);

      const result = await response.json();
      expect(result.partner.is_active).toBe(1); // MariaDB returns 1 for true
    });
  });

  describe('POST /api/partners/:id/deactivate', () => {
    it('should deactivate partner', async () => {
      const response = await app.request(`/api/partners/${testPartnerId}/deactivate`, {
        method: 'POST',
      });

      expect(response.status).toBe(200);

      const result = await response.json();
      expect(result.partner.is_active).toBe(0); // MariaDB returns 0 for false
    });
  });

  describe('GET /api/partners/statistics', () => {
    it('should get partner statistics', async () => {
      const response = await app.request('/api/partners/statistics');

      expect(response.status).toBe(200);

      const stats = await response.json();
      expect(parseInt(stats.total_partners)).toBeGreaterThan(0);
      expect(parseInt(stats.active_partners)).toBeGreaterThanOrEqual(0);
      expect(parseInt(stats.inactive_partners)).toBeGreaterThanOrEqual(0);
    });
  });

  describe('GET /api/partners/specializations', () => {
    it('should get available specializations', async () => {
      const response = await app.request('/api/partners/specializations');

      expect(response.status).toBe(200);

      const specializations = await response.json();
      expect(Array.isArray(specializations)).toBe(true);
      expect(specializations.length).toBeGreaterThan(0);
      expect(specializations).toContain('mechanical');
      expect(specializations).toContain('electrical');
    });
  });

  describe('GET /api/partners/:id/requests', () => {
    it('should get partner requests', async () => {
      const response = await app.request(`/api/partners/${testPartnerId}/requests`);

      expect(response.status).toBe(200);

      const requests = await response.json();
      expect(Array.isArray(requests)).toBe(true);
      // New partner should have no requests
      expect(requests.length).toBe(0);
    });

    it('should filter partner requests by status', async () => {
      const response = await app.request(
        `/api/partners/${testPartnerId}/requests?status=scheduled`
      );

      expect(response.status).toBe(200);

      const requests = await response.json();
      expect(Array.isArray(requests)).toBe(true);
    });
  });

  describe('GET /api/partners/:id/issues', () => {
    it('should get partner issues', async () => {
      const response = await app.request(`/api/partners/${testPartnerId}/issues`);

      expect(response.status).toBe(200);

      const issues = await response.json();
      expect(Array.isArray(issues)).toBe(true);
      // New partner should have no issues
      expect(issues.length).toBe(0);
    });

    it('should filter partner issues by status', async () => {
      const response = await app.request(`/api/partners/${testPartnerId}/issues?status=open`);

      expect(response.status).toBe(200);

      const issues = await response.json();
      expect(Array.isArray(issues)).toBe(true);
    });

    it('should return empty array for non-existent partner', async () => {
      const response = await app.request('/api/partners/99999/issues');

      expect(response.status).toBe(200); // Returns empty array for non-existent partner
      const issues = await response.json();
      expect(Array.isArray(issues)).toBe(true);
      expect(issues.length).toBe(0);
    });
  });

  describe('DELETE /api/partners/:id', () => {
    it('should delete partner without active requests', async () => {
      const response = await app.request(`/api/partners/${testPartnerId}`, { method: 'DELETE' });

      expect(response.status).toBe(200);

      const result = await response.json();
      expect(result.message).toContain('deleted successfully');

      // Verify partner is deleted
      const getResponse = await app.request(`/api/partners/${testPartnerId}`);
      expect(getResponse.status).toBe(404);

      testPartnerId = null; // Prevent cleanup attempt
    });

    it('should return 404 for non-existent partner', async () => {
      const response = await app.request('/api/partners/99999', { method: 'DELETE' });

      expect(response.status).toBe(404);
    });
  });
});
