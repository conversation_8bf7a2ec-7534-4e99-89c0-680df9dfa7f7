import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { app } from '../../../server.js';

describe('Maintenance Requests API', () => {
  beforeEach(async () => {
    // Clean up test data
  });

  afterEach(async () => {
    // Clean up after tests
  });

  describe('POST /api/maintenance', () => {
    it('should create a new maintenance request', async () => {
      // First create a test machine
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-MAINT-${timestamp}`,
        name: 'Test Machine for Maintenance',
        location: 'Test Location',
        status: 'online',
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData),
      });

      expect(createMachineResponse.status).toBe(201);
      const machine = await createMachineResponse.json();

      // Now create a maintenance request for this machine
      const maintenanceData = {
        machine_id: machine.id,
        operator_number: 'OP-MAINT-123',
        operator_name: 'Test Maintenance Operator',
        maintenance_type: 'preventive',
        urgency: 'medium',
        title: 'Scheduled Oil Change',
        description: 'Regular preventive maintenance - oil change required',
        requested_date: '2024-01-15',
      };

      const response = await app.request('/api/maintenance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(maintenanceData),
      });

      expect(response.status).toBe(201);
      const result = await response.json();

      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('machine_id', machine.id);
      expect(result).toHaveProperty('operator_number', 'OP-MAINT-123');
      expect(result).toHaveProperty('operator_name', 'Test Maintenance Operator');
      expect(result).toHaveProperty('maintenance_type', 'preventive');
      expect(result).toHaveProperty('urgency', 'medium');
      expect(result).toHaveProperty('title', 'Scheduled Oil Change');
      expect(result).toHaveProperty(
        'description',
        'Regular preventive maintenance - oil change required'
      );
      expect(result).toHaveProperty('status', 'requested');
      expect(result).toHaveProperty('machine_number', `M-MAINT-${timestamp}`);
    });

    it('should require required fields', async () => {
      const incompleteMaintenanceData = {
        operator_number: 'OP-123',
        // Missing machine_id, title
      };

      const response = await app.request('/api/maintenance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(incompleteMaintenanceData),
      });

      expect(response.status).toBe(400);
      const result = await response.json();
      expect(result).toHaveProperty('error');
    });
  });

  describe('GET /api/maintenance', () => {
    it('should get all maintenance requests', async () => {
      const response = await app.request('/api/maintenance');

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should filter maintenance requests by machine_id', async () => {
      // Create test machine and maintenance request first
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-FILTER-MAINT-${timestamp}`,
        name: 'Test Machine for Filter',
        location: 'Test Location',
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData),
      });

      const machine = await createMachineResponse.json();

      const maintenanceData = {
        machine_id: machine.id,
        operator_number: 'OP-FILTER-MAINT',
        title: 'Filter Test Maintenance',
      };

      await app.request('/api/maintenance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(maintenanceData),
      });

      // Now filter by machine_id
      const response = await app.request(`/api/maintenance?machine_id=${machine.id}`);

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(Array.isArray(result)).toBe(true);

      // All results should be for this machine
      result.forEach(request => {
        expect(request.machine_id).toBe(machine.id);
      });
    });
  });

  describe('GET /api/maintenance/:id', () => {
    it('should get maintenance request by id', async () => {
      // Create test maintenance request first
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-GET-MAINT-${timestamp}`,
        name: 'Test Machine for GET',
        location: 'Test Location',
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData),
      });

      const machine = await createMachineResponse.json();

      const maintenanceData = {
        machine_id: machine.id,
        operator_number: 'OP-GET-MAINT',
        title: 'GET Test Maintenance',
      };

      const createResponse = await app.request('/api/maintenance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(maintenanceData),
      });

      const maintenanceRequest = await createResponse.json();

      // Now get the maintenance request by ID
      const response = await app.request(`/api/maintenance/${maintenanceRequest.id}`);

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('id', maintenanceRequest.id);
      expect(result).toHaveProperty('title', 'GET Test Maintenance');
      expect(result).toHaveProperty('machine_number', `M-GET-MAINT-${timestamp}`);
    });

    it('should return 404 for non-existent maintenance request', async () => {
      const response = await app.request('/api/maintenance/999999');

      expect(response.status).toBe(404);
      const result = await response.json();
      expect(result).toHaveProperty('error', 'Maintenance request not found');
    });
  });

  describe('PUT /api/maintenance/:id', () => {
    it('should update maintenance request status', async () => {
      // Create test maintenance request first
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-UPDATE-MAINT-${timestamp}`,
        name: 'Test Machine for Update',
        location: 'Test Location',
      };

      const createMachineResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData),
      });

      const machine = await createMachineResponse.json();

      const maintenanceData = {
        machine_id: machine.id,
        operator_number: 'OP-UPDATE-MAINT',
        title: 'Update Test Maintenance',
        status: 'requested',
      };

      const createResponse = await app.request('/api/maintenance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(maintenanceData),
      });

      const maintenanceRequest = await createResponse.json();

      // Update the maintenance request
      const updateData = {
        status: 'scheduled',
        assigned_to: 'Maintenance Team',
        scheduled_date: '2024-01-20',
      };

      const response = await app.request(`/api/maintenance/${maintenanceRequest.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData),
      });

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('status', 'scheduled');
      expect(result).toHaveProperty('assigned_to', 'Maintenance Team');
    });
  });

  describe('GET /api/maintenance/statistics', () => {
    it('should get maintenance statistics', async () => {
      const response = await app.request('/api/maintenance/statistics');

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('total_requests');
      expect(result).toHaveProperty('pending_requests');
      expect(result).toHaveProperty('scheduled_requests');
      expect(result).toHaveProperty('completed_requests');
      expect(result).toHaveProperty('urgent_requests');
    });
  });
});
