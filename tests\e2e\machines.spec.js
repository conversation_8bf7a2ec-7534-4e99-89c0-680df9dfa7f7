import { test, expect } from '@playwright/test';

test.describe('Machine Management (US-001)', () => {
  test('should add new machine with QR code generation', async ({ page }) => {
    // Navigate to admin dashboard
    await page.goto('/admin');
    
    // Click add machine button
    await page.click('[data-testid="add-machine-btn"]');
    
    // Fill machine form (server-side rendered)
    await page.fill('[name="machine_number"]', 'M-001');
    await page.fill('[name="name"]', 'Test Freespink');
    await page.fill('[name="manufacturer"]', 'HAAS');
    await page.fill('[name="model"]', 'VF-2');
    await page.fill('[name="department"]', 'Tsehh A');
    await page.fill('[name="location"]', 'Rida 1');
    
    // Submit form (traditional form submit)
    await page.click('[type="submit"]');
    
    // Should redirect to machine detail view
    await expect(page).toHaveURL(/\/machines\/\d+/);
    
    // Should show success message
    await expect(page.locator('.alert-success')).toContainText('Masin edukalt lisatud');
    
    // Should display machine details
    await expect(page.locator('[data-testid="machine-number"]')).toContainText('M-001');
    await expect(page.locator('[data-testid="machine-name"]')).toContainText('Test Freespink');
    
    // Should show QR code
    await expect(page.locator('[data-testid="qr-code"]')).toBeVisible();
  });

  test('should download machine QR code (US-002)', async ({ page }) => {
    // First create a machine (assuming it exists with id=1)
    await page.goto('/machines/1');
    
    // QR code should be visible
    await expect(page.locator('[data-testid="qr-code"]')).toBeVisible();
    
    // Download QR code
    const downloadPromise = page.waitForDownload();
    await page.click('[data-testid="download-qr"]');
    const download = await downloadPromise;
    
    // Check filename
    expect(download.suggestedFilename()).toMatch(/machine_.*_qr\.png/);
  });

  test('should edit machine details (US-003)', async ({ page }) => {
    await page.goto('/machines/1');
    
    // Click edit button
    await page.click('[data-testid="edit-machine"]');
    
    // Update machine details
    await page.fill('[name="name"]', 'Uuendatud Freespink');
    await page.fill('[name="location"]', 'Tsehh B');
    
    // Submit form
    await page.click('[type="submit"]');
    
    // Should show success message
    await expect(page.locator('.alert-success')).toContainText('Andmed edukalt uuendatud');
    
    // Should display updated details
    await expect(page.locator('[data-testid="machine-name"]')).toContainText('Uuendatud Freespink');
  });
});
