import { pool } from '../config/database.js';

export class MachineGroup {
  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT 
          mg.*,
          COUNT(m.id) as machine_count
        FROM machine_groups mg
        LEFT JOIN machines m ON mg.id = m.group_id
      `;

      const params = [];
      const conditions = [];

      if (filters.is_active !== undefined) {
        conditions.push('mg.is_active = ?');
        params.push(filters.is_active);
      }

      if (filters.search) {
        conditions.push('(mg.name LIKE ? OR mg.description LIKE ?)');
        params.push(`%${filters.search}%`, `%${filters.search}%`);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' GROUP BY mg.id ORDER BY mg.name ASC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to fetch machine groups: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT 
          mg.*,
          COUNT(m.id) as machine_count
         FROM machine_groups mg
         LEFT JOIN machines m ON mg.id = m.group_id
         WHERE mg.id = ?
         GROUP BY mg.id`,
        [id]
      );

      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      throw new Error(`Failed to find machine group: ${error.message}`);
    }
  }

  static async create(groupData) {
    try {
      const { name, description, color, icon, is_active } = groupData;

      // Validate required fields
      if (!name) {
        throw new Error('Group name is required');
      }

      const [result] = await pool.execute(
        `INSERT INTO machine_groups (name, description, color, icon, is_active) 
         VALUES (?, ?, ?, ?, ?)`,
        [
          name,
          description || null,
          color || '#3B82F6',
          icon || 'fas fa-cogs',
          is_active !== undefined ? is_active : true,
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Machine group with this name already exists');
      }
      throw new Error(`Failed to create machine group: ${error.message}`);
    }
  }

  static async update(id, updateData) {
    try {
      const { name, description, color, icon, is_active } = updateData;

      const [result] = await pool.execute(
        `UPDATE machine_groups 
         SET name = ?, description = ?, color = ?, icon = ?, is_active = ?
         WHERE id = ?`,
        [name, description, color, icon, is_active, id]
      );

      if (result.affectedRows === 0) {
        return null;
      }

      return await this.findById(id);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Machine group with this name already exists');
      }
      throw new Error(`Failed to update machine group: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      // Check if group has machines
      const [machineCheck] = await pool.execute(
        'SELECT COUNT(*) as count FROM machines WHERE group_id = ?',
        [id]
      );

      if (machineCheck[0].count > 0) {
        throw new Error(
          'Cannot delete group that contains machines. Please move machines to another group first.'
        );
      }

      const [result] = await pool.execute('DELETE FROM machine_groups WHERE id = ?', [id]);

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete machine group: ${error.message}`);
    }
  }

  static async getStatistics() {
    try {
      const [stats] = await pool.execute(`
        SELECT 
          COUNT(DISTINCT mg.id) as total_groups,
          COUNT(DISTINCT CASE WHEN mg.is_active = 1 THEN mg.id END) as active_groups,
          COUNT(DISTINCT m.id) as total_machines_in_groups,
          COUNT(DISTINCT CASE WHEN m.group_id IS NULL THEN m.id END) as ungrouped_machines
        FROM machine_groups mg
        LEFT JOIN machines m ON mg.id = m.group_id
      `);

      return stats[0];
    } catch (error) {
      throw new Error(`Failed to get machine group statistics: ${error.message}`);
    }
  }

  static async getGroupMachines(groupId, filters = {}) {
    try {
      let query = `
        SELECT m.*, mg.name as group_name, mg.color as group_color, mg.icon as group_icon
        FROM machines m
        JOIN machine_groups mg ON m.group_id = mg.id
        WHERE m.group_id = ?
      `;

      const params = [groupId];

      if (filters.status) {
        query += ' AND m.status = ?';
        params.push(filters.status);
      }

      query += ' ORDER BY m.machine_number ASC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to get group machines: ${error.message}`);
    }
  }

  static async activate(id) {
    try {
      const [result] = await pool.execute(
        'UPDATE machine_groups SET is_active = TRUE WHERE id = ?',
        [id]
      );

      if (result.affectedRows === 0) {
        return null;
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to activate machine group: ${error.message}`);
    }
  }

  static async deactivate(id) {
    try {
      const [result] = await pool.execute(
        'UPDATE machine_groups SET is_active = FALSE WHERE id = ?',
        [id]
      );

      if (result.affectedRows === 0) {
        return null;
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to deactivate machine group: ${error.message}`);
    }
  }

  static async getAvailableIcons() {
    return [
      'fas fa-industry',
      'fas fa-cogs',
      'fas fa-microchip',
      'fas fa-box',
      'fas fa-search',
      'fas fa-truck',
      'fas fa-tools',
      'fas fa-wrench',
      'fas fa-hammer',
      'fas fa-bolt',
      'fas fa-fire',
      'fas fa-snowflake',
      'fas fa-tint',
      'fas fa-leaf',
      'fas fa-recycle',
      'fas fa-shield-alt',
      'fas fa-chart-line',
      'fas fa-database',
      'fas fa-server',
      'fas fa-laptop',
    ];
  }

  static async getAvailableColors() {
    return [
      '#3B82F6', // Blue
      '#10B981', // Green
      '#F59E0B', // Yellow
      '#EF4444', // Red
      '#8B5CF6', // Purple
      '#06B6D4', // Cyan
      '#F97316', // Orange
      '#84CC16', // Lime
      '#EC4899', // Pink
      '#6B7280', // Gray
      '#1F2937', // Dark Gray
      '#7C3AED', // Violet
      '#059669', // Emerald
      '#DC2626', // Red 600
      '#2563EB', // Blue 600
      '#7C2D12', // Orange 900
    ];
  }
}
