import { Hono } from 'hono';
import { serveStatic } from 'hono/bun';
import { testConnection } from './src/config/database.js';

// Import routes
import { pageRoutes } from './src/routes/pages.js';
import { machineApiRoutes } from './src/routes/api/machines.js';
import { machineGroupApiRoutes } from './src/routes/api/machine-groups.js';
import { fileRoutes } from './src/routes/api/files.js';
import { operatorApiRoutes } from './src/routes/api/operator.js';
import { issueApiRoutes } from './src/routes/api/issues.js';
import { maintenanceApiRoutes } from './src/routes/api/maintenance.js';
import { notificationApiRoutes } from './src/routes/api/notifications.js';
import { partnerApiRoutes } from './src/routes/api/partners.js';
import { partsApiRoutes } from './src/routes/api/parts.js';
import { documentsApiRoutes } from './src/routes/api/documents.js';
import { reportsRoutes } from './src/routes/api/reports.js';
import { exportRoutes } from './src/routes/api/export.js';
import { projectApiRoutes } from './src/routes/api/projects.js';

// Import services
import { notificationScheduler } from './src/services/NotificationScheduler.js';

const app = new Hono();

// Serve static files
app.use('/public/*', serveStatic({ root: './' }));

// Page routes (HTML)
app.route('/', pageRoutes);

// API routes (JSON)
app.route('/api/machines', machineApiRoutes);
app.route('/api/machine-groups', machineGroupApiRoutes);
app.route('/api/files', fileRoutes);
app.route('/api/operator', operatorApiRoutes);
app.route('/api/issues', issueApiRoutes);
app.route('/api/maintenance', maintenanceApiRoutes);
app.route('/api/notifications', notificationApiRoutes);
app.route('/api/partners', partnerApiRoutes);
app.route('/api/parts', partsApiRoutes);
app.route('/api/documents', documentsApiRoutes);
app.route('/api/reports', reportsRoutes);
app.route('/api/export', exportRoutes);
app.route('/api/projects', projectApiRoutes);

// Health check
app.get('/health', c => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 404 handler
app.notFound(c => {
  return c.text('Not Found', 404);
});

// Error handler
app.onError((err, c) => {
  console.error('Server error:', err);
  return c.text('Internal Server Error', 500);
});

// Start server
const port = process.env.PORT || 8080;

if (import.meta.main) {
  // Test database connection
  await testConnection();

  console.log(`🚀 CMMS Server starting on port ${port}`);

  // Start notification scheduler
  if (process.env.NOTIFICATIONS_ENABLED === 'true') {
    notificationScheduler.start();
  }

  Bun.serve({
    port,
    hostname: '0.0.0.0', // Listen on all interfaces
    fetch: app.fetch,
    idleTimeout: 60, // 60 seconds timeout for file uploads
    maxRequestBodySize: 50 * 1024 * 1024, // 50MB max request size
  });
}

export { app };
