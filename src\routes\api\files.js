import { Hono } from 'hono';
import { Machine } from '../../models/Machine.js';

export const fileRoutes = new Hono();

// GET /api/files/qr/:machineId - Download machine QR code
fileRoutes.get('/qr/:machineId', async (c) => {
  try {
    const machineId = parseInt(c.req.param('machineId'));
    const qrData = await Machine.getQRCode(machineId);
    
    if (!qrData || !qrData.qr_code_data) {
      return c.json({ error: 'QR code not found' }, 404);
    }
    
    const filename = `machine_${qrData.machine_number}_qr.png`;
    
    // Set proper headers for file download
    c.header('Content-Type', qrData.qr_code_mime_type || 'image/png');
    c.header('Content-Disposition', `attachment; filename="${filename}"`);
    c.header('Content-Length', qrData.qr_code_data.length.toString());
    
    return c.body(qrData.qr_code_data);
  } catch (error) {
    console.error('Error downloading QR code:', error);
    return c.json({ error: 'Failed to download QR code' }, 500);
  }
});
