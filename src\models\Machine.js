import { pool } from '../config/database.js';
import { generateQRCode } from '../utils/qrGenerator.js';

export class Machine {
  static async create(machineData) {
    const {
      machine_number,
      name,
      manufacturer,
      model,
      serial_number,
      manufacturing_year,
      department,
      responsible_operator,
      location,
      status = 'online'
    } = machineData;

    try {
      // Insert machine into database
      const [result] = await pool.execute(
        `INSERT INTO machines (
          machine_number, name, manufacturer, model, serial_number,
          manufacturing_year, department, responsible_operator, location, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          machine_number,
          name,
          manufacturer || null,
          model || null,
          serial_number || null,
          manufacturing_year || null,
          department || null,
          responsible_operator || null,
          location || null,
          status
        ]
      );

      const machineId = result.insertId;

      // Generate QR code
      const qrCodeData = await generateQRCode(machine_number);

      // Update machine with QR code
      await pool.execute(
        'UPDATE machines SET qr_code_data = ?, qr_code_mime_type = ? WHERE id = ?',
        [qrCodeData, 'image/png', machineId]
      );

      // Return the created machine
      return await this.findById(machineId);
    } catch (error) {
      throw new Error(`Failed to create machine: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          m.*,
          mg.name as group_name,
          mg.color as group_color,
          mg.icon as group_icon
         FROM machines m
         LEFT JOIN machine_groups mg ON m.group_id = mg.id
         WHERE m.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find machine: ${error.message}`);
    }
  }

  static async findByMachineNumber(machineNumber) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM machines WHERE machine_number = ?',
        [machineNumber]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find machine by number: ${error.message}`);
    }
  }

  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT
          m.*,
          mg.name as group_name,
          mg.color as group_color,
          mg.icon as group_icon
        FROM machines m
        LEFT JOIN machine_groups mg ON m.group_id = mg.id
      `;
      const params = [];
      const conditions = [];

      if (filters.status) {
        conditions.push('m.status = ?');
        params.push(filters.status);
      }

      if (filters.location) {
        conditions.push('m.location LIKE ?');
        params.push(`%${filters.location}%`);
      }

      if (filters.group_id) {
        conditions.push('m.group_id = ?');
        params.push(filters.group_id);
      }

      if (filters.search) {
        conditions.push('(m.machine_number LIKE ? OR m.name LIKE ?)');
        params.push(`%${filters.search}%`, `%${filters.search}%`);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' ORDER BY m.machine_number ASC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to fetch machines: ${error.message}`);
    }
  }

  static async update(id, updateData) {
    const allowedFields = [
      'name', 'manufacturer', 'model', 'serial_number',
      'manufacturing_year', 'department', 'responsible_operator',
      'location', 'status', 'last_maintenance', 'next_maintenance',
      'warranty_end', 'group_id'
    ];

    const fields = [];
    const values = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (fields.length === 0) {
      throw new Error('No valid fields to update');
    }

    values.push(id);

    try {
      await pool.execute(
        `UPDATE machines SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update machine: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.execute(
        'DELETE FROM machines WHERE id = ?',
        [id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete machine: ${error.message}`);
    }
  }

  static async getQRCode(id) {
    try {
      const [rows] = await pool.execute(
        'SELECT qr_code_data, qr_code_mime_type, machine_number FROM machines WHERE id = ?',
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to get QR code: ${error.message}`);
    }
  }

  static async regenerateQRCode(id) {
    try {
      const machine = await this.findById(id);
      if (!machine) {
        throw new Error('Machine not found');
      }

      // Generate new QR code with current BASE_URL
      const qrCodeData = await generateQRCode(machine.machine_number);

      // Update machine with new QR code
      await pool.execute(
        'UPDATE machines SET qr_code_data = ?, qr_code_mime_type = ? WHERE id = ?',
        [qrCodeData, 'image/png', id]
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to regenerate QR code: ${error.message}`);
    }
  }

  static async regenerateAllQRCodes() {
    try {
      const machines = await this.findAll();
      const results = [];

      for (const machine of machines) {
        try {
          await this.regenerateQRCode(machine.id);
          results.push({ id: machine.id, machine_number: machine.machine_number, success: true });
        } catch (error) {
          results.push({ id: machine.id, machine_number: machine.machine_number, success: false, error: error.message });
        }
      }

      return results;
    } catch (error) {
      throw new Error(`Failed to regenerate QR codes: ${error.message}`);
    }
  }
}
