import { Hono } from 'hono';
import { MaintenancePartner } from '../../models/MaintenancePartner.js';

export const partnerApiRoutes = new Hono();

// Specific routes first (before parameterized routes)

// GET /api/partners/statistics - Get partner statistics
partnerApiRoutes.get('/statistics', async c => {
  try {
    const stats = await MaintenancePartner.getStatistics();
    return c.json(stats);
  } catch (error) {
    console.error('Error fetching partner statistics:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// GET /api/partners/specializations - Get available specializations
partnerApiRoutes.get('/specializations', async c => {
  try {
    const specializations = await MaintenancePartner.getAvailableSpecializations();
    return c.json(specializations);
  } catch (error) {
    console.error('Error fetching specializations:', error);
    return c.json({ error: 'Failed to fetch specializations' }, 500);
  }
});

// POST /api/partners - Create a new maintenance partner
partnerApiRoutes.post('/', async c => {
  try {
    const body = await c.req.json();

    // Validate required fields
    const { company_name, email } = body;

    if (!company_name || !email) {
      return c.json(
        {
          error: 'Missing required fields: company_name, email',
        },
        400
      );
    }

    // Check if email already exists
    const existingPartner = await MaintenancePartner.findByEmail(email);
    if (existingPartner) {
      return c.json(
        {
          error: 'Partner with this email already exists',
        },
        400
      );
    }

    const partner = await MaintenancePartner.create(body);
    return c.json(partner, 201);
  } catch (error) {
    console.error('Error creating maintenance partner:', error);
    return c.json({ error: 'Failed to create maintenance partner' }, 500);
  }
});

// GET /api/partners/:id - Get maintenance partner by ID (must be after specific routes)
partnerApiRoutes.get('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));

    // Check if id is actually a number (not a string like 'statistics')
    if (isNaN(id)) {
      return c.json({ error: 'Invalid partner ID' }, 400);
    }

    const partner = await MaintenancePartner.findById(id);

    if (!partner) {
      return c.json({ error: 'Maintenance partner not found' }, 404);
    }

    return c.json(partner);
  } catch (error) {
    console.error('Error fetching maintenance partner:', error);
    return c.json({ error: 'Failed to fetch maintenance partner' }, 500);
  }
});

// GET /api/partners - Get all maintenance partners with optional filtering
partnerApiRoutes.get('/', async c => {
  try {
    const query = c.req.query();
    const filters = {};

    // Parse query parameters for filtering
    if (query.is_active !== undefined) {
      filters.is_active = query.is_active === 'true';
    }

    if (query.specialization) {
      filters.specialization = query.specialization;
    }

    if (query.search) {
      filters.search = query.search;
    }

    if (query.limit) {
      filters.limit = parseInt(query.limit);
    }

    const partners = await MaintenancePartner.findAll(filters);
    return c.json(partners);
  } catch (error) {
    console.error('Error fetching maintenance partners:', error);
    return c.json({ error: 'Failed to fetch maintenance partners' }, 500);
  }
});

// GET /api/partners/:id/requests - Get maintenance requests for a partner
partnerApiRoutes.get('/:id/requests', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const query = c.req.query();
    const filters = {};

    if (query.status) {
      filters.status = query.status;
    }

    if (query.limit) {
      filters.limit = parseInt(query.limit);
    }

    const requests = await MaintenancePartner.getPartnerRequests(id, filters);
    return c.json(requests);
  } catch (error) {
    console.error('Error fetching partner requests:', error);
    return c.json({ error: 'Failed to fetch partner requests' }, 500);
  }
});

// GET /api/partners/:id/issues - Get issues for a partner
partnerApiRoutes.get('/:id/issues', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const query = c.req.query();
    const filters = {};

    if (query.status) {
      filters.status = query.status;
    }

    if (query.limit) {
      filters.limit = parseInt(query.limit);
    }

    const issues = await MaintenancePartner.getPartnerIssues(id, filters);
    return c.json(issues);
  } catch (error) {
    console.error('Error fetching partner issues:', error);
    return c.json({ error: 'Failed to fetch partner issues' }, 500);
  }
});

// PUT /api/partners/:id - Update maintenance partner
partnerApiRoutes.put('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const updateData = await c.req.json();

    // Check if email is being updated and if it already exists
    if (updateData.email) {
      const existingPartner = await MaintenancePartner.findByEmail(updateData.email);
      if (existingPartner && existingPartner.id !== id) {
        return c.json(
          {
            error: 'Partner with this email already exists',
          },
          400
        );
      }
    }

    const partner = await MaintenancePartner.update(id, updateData);

    if (!partner) {
      return c.json({ error: 'Maintenance partner not found' }, 404);
    }

    return c.json(partner);
  } catch (error) {
    console.error('Error updating maintenance partner:', error);
    return c.json({ error: 'Failed to update maintenance partner' }, 500);
  }
});

// POST /api/partners/:id/activate - Activate maintenance partner
partnerApiRoutes.post('/:id/activate', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const partner = await MaintenancePartner.activate(id);

    if (!partner) {
      return c.json({ error: 'Maintenance partner not found' }, 404);
    }

    return c.json({ message: 'Partner activated successfully', partner });
  } catch (error) {
    console.error('Error activating maintenance partner:', error);
    return c.json({ error: 'Failed to activate maintenance partner' }, 500);
  }
});

// POST /api/partners/:id/deactivate - Deactivate maintenance partner
partnerApiRoutes.post('/:id/deactivate', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const partner = await MaintenancePartner.deactivate(id);

    if (!partner) {
      return c.json({ error: 'Maintenance partner not found' }, 404);
    }

    return c.json({ message: 'Partner deactivated successfully', partner });
  } catch (error) {
    console.error('Error deactivating maintenance partner:', error);
    return c.json({ error: 'Failed to deactivate maintenance partner' }, 500);
  }
});

// DELETE /api/partners/:id - Delete maintenance partner
partnerApiRoutes.delete('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await MaintenancePartner.delete(id);

    if (!deleted) {
      return c.json({ error: 'Maintenance partner not found or has active requests' }, 404);
    }

    return c.json({ message: 'Maintenance partner deleted successfully' });
  } catch (error) {
    console.error('Error deleting maintenance partner:', error);

    if (error.message.includes('active maintenance requests')) {
      return c.json({ error: error.message }, 400);
    }

    return c.json({ error: 'Failed to delete maintenance partner' }, 500);
  }
});
