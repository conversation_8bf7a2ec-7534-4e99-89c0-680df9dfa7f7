import { DataTypes } from 'sequelize';
import crypto from 'crypto';
import sequelize from '../database/connection.js';

const Session = sequelize.define('Session', {
  id: {
    type: DataTypes.STRING(128),
    primaryKey: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'sessions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Instance methods
Session.prototype.isExpired = function() {
  return new Date() > this.expires_at;
};

Session.prototype.extend = async function(hours = 8) {
  this.expires_at = new Date(Date.now() + hours * 60 * 60 * 1000);
  await this.save();
};

// Static methods
Session.generateId = function() {
  return crypto.randomBytes(64).toString('hex');
};

Session.createSession = async function(userId, ipAddress, userAgent, hours = 8) {
  const sessionId = this.generateId();
  const expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);

  return await this.create({
    id: sessionId,
    user_id: userId,
    ip_address: ipAddress,
    user_agent: userAgent,
    expires_at: expiresAt
  });
};

Session.findByIdWithUser = async function(sessionId) {
  const { default: User } = await import('./User.js');

  return await this.findOne({
    where: { id: sessionId },
    include: [{
      model: User,
      as: 'user',
      where: { is_active: true }
    }]
  });
};

Session.cleanupExpired = async function() {
  const expiredCount = await this.destroy({
    where: {
      expires_at: {
        [sequelize.Sequelize.Op.lt]: new Date()
      }
    }
  });
  
  console.log(`Cleaned up ${expiredCount} expired sessions`);
  return expiredCount;
};

Session.destroyUserSessions = async function(userId) {
  return await this.destroy({
    where: { user_id: userId }
  });
};

export default Session;
