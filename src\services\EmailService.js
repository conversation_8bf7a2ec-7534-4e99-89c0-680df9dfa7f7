import nodemailer from 'nodemailer';

class EmailService {
  constructor() {
    this.transporter = null;
    this.isEnabled = process.env.NOTIFICATIONS_ENABLED === 'true';
    this.fromEmail = process.env.FROM_EMAIL || 'CMMS System <<EMAIL>>';
    this.adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    this.maintenanceTeamEmail = process.env.MAINTENANCE_TEAM_EMAIL || '<EMAIL>';

    if (this.isEnabled) {
      this.initializeTransporter();
    }
  }

  initializeTransporter() {
    try {
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });

      // Verify connection configuration
      this.transporter.verify((error, success) => {
        if (error) {
          console.error('Email service configuration error:', error);
        } else {
          console.log('✅ Email service ready');
        }
      });
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
      this.isEnabled = false;
    }
  }

  async sendEmail(to, subject, html, text = null) {
    if (!this.isEnabled || !this.transporter) {
      console.log('Email notifications disabled or not configured');
      return false;
    }

    try {
      const mailOptions = {
        from: this.fromEmail,
        to: to,
        subject: subject,
        html: html,
        text: text || this.stripHtml(html),
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '');
  }

  // Issue notification methods
  async notifyNewIssue(issue) {
    const subject = `🚨 Uus rike: ${issue.title}`;
    const html = this.generateIssueNotificationHtml(issue, 'new');

    const recipients = [this.adminEmail];
    if (issue.priority === 'critical') {
      recipients.push(this.maintenanceTeamEmail);
    }

    for (const email of recipients) {
      await this.sendEmail(email, subject, html);
    }
  }

  async notifyIssueStatusChange(issue, oldStatus, newStatus) {
    const subject = `🔄 Rike staatuse muutus: ${issue.title}`;
    const html = this.generateIssueStatusChangeHtml(issue, oldStatus, newStatus);

    await this.sendEmail(this.adminEmail, subject, html);
  }

  async notifyIssueResolved(issue) {
    const subject = `✅ Rike lahendatud: ${issue.title}`;
    const html = this.generateIssueResolvedHtml(issue);

    await this.sendEmail(this.adminEmail, subject, html);
  }

  // Maintenance notification methods
  async notifyNewMaintenanceRequest(maintenanceRequest) {
    const subject = `🔧 Uus hoolduse taotlus: ${maintenanceRequest.title}`;
    const html = this.generateMaintenanceNotificationHtml(maintenanceRequest, 'new');

    const recipients = [this.adminEmail, this.maintenanceTeamEmail];
    if (maintenanceRequest.urgency === 'urgent') {
      // Add additional urgent notifications if needed
    }

    for (const email of recipients) {
      await this.sendEmail(email, subject, html);
    }
  }

  async notifyMaintenanceStatusChange(maintenanceRequest, oldStatus, newStatus) {
    const subject = `🔄 Hoolduse staatuse muutus: ${maintenanceRequest.title}`;
    const html = this.generateMaintenanceStatusChangeHtml(maintenanceRequest, oldStatus, newStatus);

    await this.sendEmail(this.maintenanceTeamEmail, subject, html);
  }

  async notifyMaintenanceScheduled(maintenanceRequest) {
    const subject = `📅 Hooldus planeeritud: ${maintenanceRequest.title}`;
    const html = this.generateMaintenanceScheduledHtml(maintenanceRequest);

    await this.sendEmail(this.maintenanceTeamEmail, subject, html);
  }

  async notifyMaintenanceReminder(maintenanceRequest) {
    const subject = `⏰ Hoolduse meeldetuletus: ${maintenanceRequest.title}`;
    const html = this.generateMaintenanceReminderHtml(maintenanceRequest);

    await this.sendEmail(this.maintenanceTeamEmail, subject, html);
  }

  // Partner notification methods
  async notifyPartnerAssignment(maintenanceRequest) {
    if (!maintenanceRequest.partner_email) {
      console.log('No partner email available for notification');
      return;
    }

    const subject = `🔧 Uus hoolduse taotlus: ${maintenanceRequest.title}`;
    const html = this.generatePartnerAssignmentHtml(maintenanceRequest);

    await this.sendEmail(maintenanceRequest.partner_email, subject, html);
  }

  async notifyPartnerStatusChange(maintenanceRequest, oldStatus, newStatus) {
    if (!maintenanceRequest.partner_email) {
      console.log('No partner email available for notification');
      return;
    }

    const subject = `🔄 Hoolduse staatuse muutus: ${maintenanceRequest.title}`;
    const html = this.generatePartnerStatusChangeHtml(maintenanceRequest, oldStatus, newStatus);

    await this.sendEmail(maintenanceRequest.partner_email, subject, html);
  }

  // Issue partner notification methods
  async notifyIssuePartnerAssignment(issue) {
    if (!issue.partner_email) {
      console.log('No partner email available for issue notification');
      return;
    }

    const subject = `🚨 Uus rike määratud: ${issue.title}`;
    const html = this.generateIssuePartnerAssignmentHtml(issue);

    await this.sendEmail(issue.partner_email, subject, html);
  }

  async notifyIssuePartnerStatusChange(issue, oldStatus, newStatus) {
    if (!issue.partner_email) {
      console.log('No partner email available for issue notification');
      return;
    }

    const subject = `🔄 Rikke staatuse muutus: ${issue.title}`;
    const html = this.generateIssuePartnerStatusChangeHtml(issue, oldStatus, newStatus);

    await this.sendEmail(issue.partner_email, subject, html);
  }

  // HTML template generators
  generateIssueNotificationHtml(issue, type) {
    const priorityColor = {
      low: '#10B981',
      medium: '#F59E0B',
      high: '#F97316',
      critical: '#EF4444',
    };

    const statusColor = {
      open: '#EF4444',
      in_progress: '#F59E0B',
      resolved: '#10B981',
      closed: '#6B7280',
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #EF4444; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 ${type === 'new' ? 'Uus rike teatatud' : 'Rike uuendus'}</h1>
          </div>
          <div class="content">
            <h2>${issue.title}</h2>
            <p><strong>Masin:</strong> ${issue.machine_number} - ${issue.machine_name}</p>
            <p><strong>Asukoht:</strong> ${issue.machine_location || 'Määramata'}</p>
            <p><strong>Operaator:</strong> ${issue.operator_number} ${issue.operator_name ? '(' + issue.operator_name + ')' : ''}</p>
            <p><strong>Prioriteet:</strong> <span class="badge" style="background: ${priorityColor[issue.priority] || '#6B7280'}; color: white;">${issue.priority}</span></p>
            <p><strong>Staatus:</strong> <span class="badge" style="background: ${statusColor[issue.status] || '#6B7280'}; color: white;">${issue.status}</span></p>
            <p><strong>Tüüp:</strong> ${issue.issue_type}</p>
            ${issue.description ? `<p><strong>Kirjeldus:</strong><br>${issue.description}</p>` : ''}
            <p><strong>Teatatud:</strong> ${new Date(issue.reported_at).toLocaleString('et-EE')}</p>

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/issues/${issue.id}" class="button">Vaata rikke detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateIssueStatusChangeHtml(issue, oldStatus, newStatus) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .status-change { background: #E5E7EB; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔄 Rike staatuse muutus</h1>
          </div>
          <div class="content">
            <h2>${issue.title}</h2>
            <p><strong>Masin:</strong> ${issue.machine_number} - ${issue.machine_name}</p>

            <div class="status-change">
              <p><strong>Staatuse muutus:</strong></p>
              <p>🔸 <strong>Vana staatus:</strong> ${oldStatus}</p>
              <p>🔸 <strong>Uus staatus:</strong> ${newStatus}</p>
              <p><strong>Muudetud:</strong> ${new Date().toLocaleString('et-EE')}</p>
            </div>

            ${issue.assigned_to ? `<p><strong>Määratud:</strong> ${issue.assigned_to}</p>` : ''}
            ${issue.resolution_notes ? `<p><strong>Lahenduse märkused:</strong><br>${issue.resolution_notes}</p>` : ''}

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/issues/${issue.id}" class="button">Vaata rikke detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateIssueResolvedHtml(issue) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #10B981; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .success { background: #D1FAE5; border: 1px solid #10B981; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Rike lahendatud</h1>
          </div>
          <div class="content">
            <div class="success">
              <h2>🎉 Rike edukalt lahendatud!</h2>
              <p><strong>${issue.title}</strong></p>
            </div>

            <p><strong>Masin:</strong> ${issue.machine_number} - ${issue.machine_name}</p>
            <p><strong>Lahendaja:</strong> ${issue.assigned_to || 'Määramata'}</p>
            <p><strong>Lahendatud:</strong> ${new Date().toLocaleString('et-EE')}</p>

            ${
              issue.resolution_notes
                ? `
              <div style="background: #F3F4F6; padding: 15px; border-radius: 8px; margin: 15px 0;">
                <p><strong>Lahenduse kirjeldus:</strong></p>
                <p>${issue.resolution_notes}</p>
              </div>
            `
                : ''
            }

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/issues/${issue.id}" class="button">Vaata rikke detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateMaintenanceNotificationHtml(maintenanceRequest, type) {
    const urgencyColor = {
      low: '#10B981',
      medium: '#F59E0B',
      high: '#F97316',
      urgent: '#EF4444',
    };

    const statusColor = {
      requested: '#F59E0B',
      scheduled: '#3B82F6',
      in_progress: '#F97316',
      completed: '#10B981',
      cancelled: '#6B7280',
    };

    const typeColor = {
      preventive: '#10B981',
      corrective: '#F59E0B',
      emergency: '#EF4444',
      inspection: '#3B82F6',
      calibration: '#8B5CF6',
      cleaning: '#06B6D4',
      other: '#6B7280',
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F97316; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔧 ${type === 'new' ? 'Uus hoolduse taotlus' : 'Hoolduse uuendus'}</h1>
          </div>
          <div class="content">
            <h2>${maintenanceRequest.title}</h2>
            <p><strong>Masin:</strong> ${maintenanceRequest.machine_number} - ${maintenanceRequest.machine_name}</p>
            <p><strong>Asukoht:</strong> ${maintenanceRequest.machine_location || 'Määramata'}</p>
            <p><strong>Operaator:</strong> ${maintenanceRequest.operator_number} ${maintenanceRequest.operator_name ? '(' + maintenanceRequest.operator_name + ')' : ''}</p>
            <p><strong>Tüüp:</strong> <span class="badge" style="background: ${typeColor[maintenanceRequest.maintenance_type] || '#6B7280'}; color: white;">${maintenanceRequest.maintenance_type}</span></p>
            <p><strong>Kiireloomulisus:</strong> <span class="badge" style="background: ${urgencyColor[maintenanceRequest.urgency] || '#6B7280'}; color: white;">${maintenanceRequest.urgency}</span></p>
            <p><strong>Staatus:</strong> <span class="badge" style="background: ${statusColor[maintenanceRequest.status] || '#6B7280'}; color: white;">${maintenanceRequest.status}</span></p>
            ${maintenanceRequest.description ? `<p><strong>Kirjeldus:</strong><br>${maintenanceRequest.description}</p>` : ''}
            <p><strong>Taotletud:</strong> ${maintenanceRequest.requested_date ? new Date(maintenanceRequest.requested_date).toLocaleDateString('et-EE') : new Date(maintenanceRequest.created_at).toLocaleDateString('et-EE')}</p>
            ${maintenanceRequest.scheduled_date ? `<p><strong>Planeeritud:</strong> ${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</p>` : ''}
            ${maintenanceRequest.assigned_to ? `<p><strong>Määratud:</strong> ${maintenanceRequest.assigned_to}</p>` : ''}

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/maintenance/${maintenanceRequest.id}" class="button">Vaata hoolduse detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateMaintenanceStatusChangeHtml(maintenanceRequest, oldStatus, newStatus) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .status-change { background: #E5E7EB; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔄 Hoolduse staatuse muutus</h1>
          </div>
          <div class="content">
            <h2>${maintenanceRequest.title}</h2>
            <p><strong>Masin:</strong> ${maintenanceRequest.machine_number} - ${maintenanceRequest.machine_name}</p>

            <div class="status-change">
              <p><strong>Staatuse muutus:</strong></p>
              <p>🔸 <strong>Vana staatus:</strong> ${oldStatus}</p>
              <p>🔸 <strong>Uus staatus:</strong> ${newStatus}</p>
              <p><strong>Muudetud:</strong> ${new Date().toLocaleString('et-EE')}</p>
            </div>

            ${maintenanceRequest.assigned_to ? `<p><strong>Määratud:</strong> ${maintenanceRequest.assigned_to}</p>` : ''}
            ${maintenanceRequest.scheduled_date ? `<p><strong>Planeeritud kuupäev:</strong> ${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</p>` : ''}

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/maintenance/${maintenanceRequest.id}" class="button">Vaata hoolduse detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateMaintenanceScheduledHtml(maintenanceRequest) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .scheduled { background: #DBEAFE; border: 1px solid #3B82F6; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📅 Hooldus planeeritud</h1>
          </div>
          <div class="content">
            <div class="scheduled">
              <h2>📋 Hooldus planeeritud!</h2>
              <p><strong>${maintenanceRequest.title}</strong></p>
            </div>

            <p><strong>Masin:</strong> ${maintenanceRequest.machine_number} - ${maintenanceRequest.machine_name}</p>
            <p><strong>Planeeritud kuupäev:</strong> ${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</p>
            <p><strong>Määratud:</strong> ${maintenanceRequest.assigned_to || 'Määramata'}</p>
            ${maintenanceRequest.estimated_duration ? `<p><strong>Hinnanguline kestus:</strong> ${maintenanceRequest.estimated_duration} minutit</p>` : ''}

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/maintenance/${maintenanceRequest.id}" class="button">Vaata hoolduse detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateMaintenanceReminderHtml(maintenanceRequest) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .reminder { background: #FEF3C7; border: 1px solid #F59E0B; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⏰ Hoolduse meeldetuletus</h1>
          </div>
          <div class="content">
            <div class="reminder">
              <h2>🔔 Hoolduse aeg läheneb!</h2>
              <p><strong>${maintenanceRequest.title}</strong></p>
            </div>

            <p><strong>Masin:</strong> ${maintenanceRequest.machine_number} - ${maintenanceRequest.machine_name}</p>
            <p><strong>Planeeritud kuupäev:</strong> ${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</p>
            <p><strong>Määratud:</strong> ${maintenanceRequest.assigned_to || 'Määramata'}</p>
            ${maintenanceRequest.estimated_duration ? `<p><strong>Hinnanguline kestus:</strong> ${maintenanceRequest.estimated_duration} minutit</p>` : ''}

            <p style="color: #F59E0B; font-weight: bold;">⚠️ Palun valmistuge hoolduse läbiviimiseks!</p>

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/maintenance/${maintenanceRequest.id}" class="button">Vaata hoolduse detaile</a>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generatePartnerAssignmentHtml(maintenanceRequest) {
    const urgencyColor = {
      low: '#10B981',
      medium: '#F59E0B',
      high: '#F97316',
      urgent: '#EF4444',
    };

    const typeColor = {
      preventive: '#10B981',
      corrective: '#F59E0B',
      emergency: '#EF4444',
      inspection: '#3B82F6',
      calibration: '#8B5CF6',
      cleaning: '#06B6D4',
      other: '#6B7280',
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .partner-info { background: #E0F2FE; border: 1px solid #0284C7; padding: 15px; border-radius: 8px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔧 Uus hoolduse taotlus</h1>
            <p>Teile on määratud hoolduse taotlus</p>
          </div>
          <div class="content">
            <div class="partner-info">
              <h2>👋 Tere, ${maintenanceRequest.partner_company}!</h2>
              <p>Teile on määratud uus hoolduse taotlus meie CMMS süsteemis.</p>
            </div>

            <h2>${maintenanceRequest.title}</h2>
            <p><strong>Masin:</strong> ${maintenanceRequest.machine_number} - ${maintenanceRequest.machine_name}</p>
            <p><strong>Asukoht:</strong> ${maintenanceRequest.machine_location || 'Määramata'}</p>
            <p><strong>Tüüp:</strong> <span class="badge" style="background: ${typeColor[maintenanceRequest.maintenance_type] || '#6B7280'}; color: white;">${maintenanceRequest.maintenance_type}</span></p>
            <p><strong>Kiireloomulisus:</strong> <span class="badge" style="background: ${urgencyColor[maintenanceRequest.urgency] || '#6B7280'}; color: white;">${maintenanceRequest.urgency}</span></p>

            ${
              maintenanceRequest.description
                ? `
              <div style="background: #F3F4F6; padding: 15px; border-radius: 8px; margin: 15px 0;">
                <p><strong>Kirjeldus:</strong></p>
                <p>${maintenanceRequest.description}</p>
              </div>
            `
                : ''
            }

            ${
              maintenanceRequest.partner_notes
                ? `
              <div style="background: #FEF3C7; padding: 15px; border-radius: 8px; margin: 15px 0;">
                <p><strong>Märkused partnerile:</strong></p>
                <p>${maintenanceRequest.partner_notes}</p>
              </div>
            `
                : ''
            }

            <p><strong>Taotletud kuupäev:</strong> ${maintenanceRequest.requested_date ? new Date(maintenanceRequest.requested_date).toLocaleDateString('et-EE') : new Date(maintenanceRequest.created_at).toLocaleDateString('et-EE')}</p>
            ${maintenanceRequest.scheduled_date ? `<p><strong>Planeeritud kuupäev:</strong> ${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</p>` : ''}

            <div style="background: #F0F9FF; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3>📞 Kontaktandmed:</h3>
              <p><strong>Operaator:</strong> ${maintenanceRequest.operator_number} ${maintenanceRequest.operator_name ? '(' + maintenanceRequest.operator_name + ')' : ''}</p>
              <p><strong>Määratud:</strong> ${maintenanceRequest.assigned_to || 'Määramata'}</p>
            </div>

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/maintenance/${maintenanceRequest.id}" class="button">Vaata täielikku infot</a>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #F9FAFB; border-radius: 8px;">
              <p style="font-size: 14px; color: #6B7280;">
                <strong>Märkus:</strong> Palun kinnitage hoolduse vastuvõtmine ja planeeritud aeg võimalikult kiiresti.
                Küsimuste korral võtke ühendust meie hooldusmeeskonnaga.
              </p>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generatePartnerStatusChangeHtml(maintenanceRequest, oldStatus, newStatus) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .status-change { background: #E5E7EB; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔄 Hoolduse staatuse muutus</h1>
            <p>${maintenanceRequest.partner_company}</p>
          </div>
          <div class="content">
            <h2>${maintenanceRequest.title}</h2>
            <p><strong>Masin:</strong> ${maintenanceRequest.machine_number} - ${maintenanceRequest.machine_name}</p>

            <div class="status-change">
              <p><strong>Staatuse muutus:</strong></p>
              <p>🔸 <strong>Vana staatus:</strong> ${oldStatus}</p>
              <p>🔸 <strong>Uus staatus:</strong> ${newStatus}</p>
              <p><strong>Muudetud:</strong> ${new Date().toLocaleString('et-EE')}</p>
            </div>

            ${maintenanceRequest.scheduled_date ? `<p><strong>Planeeritud kuupäev:</strong> ${new Date(maintenanceRequest.scheduled_date).toLocaleDateString('et-EE')}</p>` : ''}
            ${maintenanceRequest.partner_notes ? `<p><strong>Märkused:</strong> ${maintenanceRequest.partner_notes}</p>` : ''}

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/maintenance/${maintenanceRequest.id}" class="button">Vaata hoolduse detaile</a>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #F9FAFB; border-radius: 8px;">
              <p style="font-size: 14px; color: #6B7280;">
                Küsimuste korral võtke ühendust meie hooldusmeeskonnaga või logige sisse CMMS süsteemi.
              </p>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateIssuePartnerAssignmentHtml(issue) {
    const priorityColor = {
      low: '#10B981',
      medium: '#F59E0B',
      high: '#F97316',
      critical: '#EF4444',
    };

    const typeColor = {
      mechanical: '#F59E0B',
      electrical: '#3B82F6',
      hydraulic: '#8B5CF6',
      pneumatic: '#06B6D4',
      software: '#10B981',
      safety: '#EF4444',
      other: '#6B7280',
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #EF4444; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .partner-info { background: #FEE2E2; border: 1px solid #EF4444; padding: 15px; border-radius: 8px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 Uus rike määratud</h1>
            <p>Teile on määratud rikke lahendamine</p>
          </div>
          <div class="content">
            <div class="partner-info">
              <h2>👋 Tere, ${issue.partner_company}!</h2>
              <p>Teile on määratud uus rike lahendamiseks meie CMMS süsteemis.</p>
            </div>

            <h2>${issue.title}</h2>
            <p><strong>Masin:</strong> ${issue.machine_number} - ${issue.machine_name}</p>
            <p><strong>Asukoht:</strong> ${issue.machine_location || 'Määramata'}</p>
            <p><strong>Operaator:</strong> ${issue.operator_number} ${issue.operator_name ? '(' + issue.operator_name + ')' : ''}</p>
            <p><strong>Rikke tüüp:</strong> <span class="badge" style="background: ${typeColor[issue.issue_type] || '#6B7280'}; color: white;">${issue.issue_type}</span></p>
            <p><strong>Prioriteet:</strong> <span class="badge" style="background: ${priorityColor[issue.priority] || '#6B7280'}; color: white;">${issue.priority}</span></p>

            ${
              issue.description
                ? `
              <div style="background: #F3F4F6; padding: 15px; border-radius: 8px; margin: 15px 0;">
                <p><strong>Rikke kirjeldus:</strong></p>
                <p>${issue.description}</p>
              </div>
            `
                : ''
            }

            ${
              issue.partner_notes
                ? `
              <div style="background: #FEF3C7; padding: 15px; border-radius: 8px; margin: 15px 0;">
                <p><strong>Märkused partnerile:</strong></p>
                <p>${issue.partner_notes}</p>
              </div>
            `
                : ''
            }

            <p><strong>Teatatud:</strong> ${new Date(issue.reported_at).toLocaleString('et-EE')}</p>

            <div style="background: #F0F9FF; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3>📞 Kontaktandmed:</h3>
              <p><strong>Operaator:</strong> ${issue.operator_number} ${issue.operator_name ? '(' + issue.operator_name + ')' : ''}</p>
              ${issue.assigned_to ? `<p><strong>Määratud:</strong> ${issue.assigned_to}</p>` : ''}
            </div>

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/issues/${issue.id}" class="button">Vaata täielikku infot</a>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #F9FAFB; border-radius: 8px;">
              <p style="font-size: 14px; color: #6B7280;">
                <strong>Märkus:</strong> Palun kinnitage rikke vastuvõtmine ja hinnanguline lahendamise aeg võimalikult kiiresti.
                Küsimuste korral võtke ühendust meie hooldusmeeskonnaga.
              </p>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateIssuePartnerStatusChangeHtml(issue, oldStatus, newStatus) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }
          .content { background: #f9f9f9; padding: 20px; }
          .status-change { background: #E5E7EB; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
          .footer { background: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔄 Rikke staatuse muutus</h1>
            <p>${issue.partner_company}</p>
          </div>
          <div class="content">
            <h2>${issue.title}</h2>
            <p><strong>Masin:</strong> ${issue.machine_number} - ${issue.machine_name}</p>

            <div class="status-change">
              <p><strong>Staatuse muutus:</strong></p>
              <p>🔸 <strong>Vana staatus:</strong> ${oldStatus}</p>
              <p>🔸 <strong>Uus staatus:</strong> ${newStatus}</p>
              <p><strong>Muudetud:</strong> ${new Date().toLocaleString('et-EE')}</p>
            </div>

            ${issue.assigned_to ? `<p><strong>Määratud:</strong> ${issue.assigned_to}</p>` : ''}
            ${issue.partner_notes ? `<p><strong>Märkused:</strong> ${issue.partner_notes}</p>` : ''}
            ${issue.resolution_notes ? `<p><strong>Lahenduse märkused:</strong> ${issue.resolution_notes}</p>` : ''}

            <div style="margin-top: 20px;">
              <a href="${process.env.BASE_URL}/admin/issues/${issue.id}" class="button">Vaata rikke detaile</a>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #F9FAFB; border-radius: 8px;">
              <p style="font-size: 14px; color: #6B7280;">
                Küsimuste korral võtke ühendust meie hooldusmeeskonnaga või logige sisse CMMS süsteemi.
              </p>
            </div>
          </div>
          <div class="footer">
            CMMS - Computerized Maintenance Management System
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

export const emailService = new EmailService();
