-- Create maintenance partners table
CREATE TABLE IF NOT EXISTS maintenance_partners (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255),
  email VARCHAR(255) NOT NULL UNIQUE,
  phone VARCHAR(50),
  address TEXT,
  specializations TEXT, -- <PERSON>SON array of specializations
  hourly_rate DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'EUR',
  is_active BOOLEAN DEFAULT TRUE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_email (email),
  INDEX idx_active (is_active),
  INDEX idx_company_name (company_name)
);

-- Add partner_id to maintenance_requests table
ALTER TABLE maintenance_requests 
ADD COLUMN partner_id INT NULL AFTER assigned_to,
ADD COLUMN partner_notes TEXT NULL AFTER partner_id,
ADD COLUMN partner_cost DECIMAL(10,2) NULL AFTER partner_notes,
ADD FOREIGN KEY fk_maintenance_partner (partner_id) REFERENCES maintenance_partners(id) ON DELETE SET NULL;

-- Create index for partner_id
CREATE INDEX idx_partner_id ON maintenance_requests(partner_id);

-- Insert some sample maintenance partners
INSERT INTO maintenance_partners (company_name, contact_person, email, phone, address, specializations, hourly_rate, notes) VALUES
('TehnoHooldus OÜ', 'Mart Tamm', '<EMAIL>', '+372 5123 4567', 'Tallinn, Mustamäe tee 16', '["mechanical", "electrical", "hydraulic"]', 45.00, 'Spetsialiseerub tööstusseadmete hooldusele'),
('Elektripartner AS', 'Karin Kask', '<EMAIL>', '+372 5234 5678', 'Tartu, Riia 185', '["electrical", "automation", "calibration"]', 55.00, 'Elektrisüsteemide ja automaatika ekspert'),
('MehaanikaPro OÜ', 'Jaan Jõgi', '<EMAIL>', '+372 5345 6789', 'Pärnu, Papiniidu 8', '["mechanical", "pneumatic", "cleaning"]', 40.00, 'Mehhaaniliste süsteemide hooldus ja remont'),
('KvaliteetKontroll OÜ', 'Liis Lepik', '<EMAIL>', '+372 5456 7890', 'Narva, Kreenholmi 1', '["inspection", "calibration", "testing"]', 60.00, 'Seadmete kontrollimine ja kalibreerimine'),
('KiireRemont 24/7', 'Toomas Toom', '<EMAIL>', '+372 5567 8901', 'Tallinn, Peterburi tee 90', '["emergency", "mechanical", "electrical"]', 75.00, '24/7 hädahoolduse teenus');

-- Update some existing maintenance requests to have partners
UPDATE maintenance_requests 
SET partner_id = 1, partner_notes = 'Määratud TehnoHooldus OÜ-le mehhaanilise hoolduse jaoks'
WHERE id IN (SELECT id FROM (SELECT id FROM maintenance_requests WHERE maintenance_type = 'preventive' LIMIT 2) as temp);

UPDATE maintenance_requests 
SET partner_id = 2, partner_notes = 'Elektrisüsteemi kontroll vajalik'
WHERE id IN (SELECT id FROM (SELECT id FROM maintenance_requests WHERE maintenance_type = 'inspection' LIMIT 1) as temp);

UPDATE maintenance_requests 
SET partner_id = 5, partner_notes = 'Hädahooldus - kiire sekkumine vajalik'
WHERE id IN (SELECT id FROM (SELECT id FROM maintenance_requests WHERE maintenance_type = 'emergency' LIMIT 1) as temp);
