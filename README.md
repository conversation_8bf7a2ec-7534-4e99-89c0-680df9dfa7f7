# CMMS - Computerized Maintenance Management System

A modern, fullstack CMMS application built with <PERSON><PERSON>, <PERSON><PERSON>, and MariaDB. Designed to manage up to 30 machines, their maintenance, documentation, and spare parts.

## 🚀 Features

- **Machine Management**: Register and manage machines with QR codes
- **Mobile-Friendly**: QR code scanning for operators on mobile devices
- **Issue Reporting**: Report and track machine issues
- **Maintenance Planning**: Schedule and track maintenance activities
- **Spare Parts Management**: Inventory tracking with low-stock alerts
- **Document Management**: Upload/download manuals and certificates
- **Statistics & Reports**: Machine efficiency and issue analytics

## 🛠 Tech Stack

- **Runtime**: Bun
- **Backend**: Hono.js
- **Database**: MariaDB/MySQL
- **Frontend**: Server-side rendering (EJS) + Tailwind CSS + Alpine.js
- **Testing**: Playwright (E2E) + Vitest (API/Unit tests)
- **QR Codes**: qrcode library
- **File Storage**: Database LONGBLOB

## 📋 Prerequisites

- [Bun](https://bun.sh/) (latest version)
- [MariaDB](https://mariadb.org/) or MySQL
- Git

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CMMS
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

4. **Setup database**
   ```bash
   # Start MariaDB service
   # Create database and run migrations
   bun run migrate
   ```

5. **Start development server**
   ```bash
   bun run dev
   ```

6. **Access the application**
   - Admin Dashboard: http://localhost:8080/admin
   - API Health Check: http://localhost:8080/health

## 🧪 Testing

### Run API Tests
```bash
bun test
```

### Run E2E Tests
```bash
bun run test:e2e
```

### Run Tests in Watch Mode
```bash
bun run test:watch
```

## 📁 Project Structure

```
CMMS/
├── src/
│   ├── config/          # Database and app configuration
│   ├── models/          # Database models
│   ├── routes/          # API and page routes
│   ├── controllers/     # Business logic
│   ├── middleware/      # Custom middleware
│   ├── utils/           # Utility functions
│   └── views/           # EJS templates
├── tests/
│   ├── e2e/            # Playwright E2E tests
│   ├── unit/           # Vitest unit tests
│   └── fixtures/       # Test data
├── database/
│   ├── schema.sql      # Database schema
│   ├── migrate.js      # Migration script
│   └── seed.js         # Seed data
├── public/             # Static assets
└── docs/               # Documentation
```

## 🔄 Development Workflow (TDD)

This project follows Test-Driven Development:

1. **🔴 RED**: Write failing test first
2. **🟢 GREEN**: Implement minimal code to pass
3. **🔵 REFACTOR**: Improve code while keeping tests green

## 📊 Database Schema

The application uses the following main tables:
- `machines` - Machine registry with QR codes
- `operators` - Operator information
- `issues` - Issue/maintenance reports
- `maintenance_records` - Maintenance history
- `spare_parts` - Parts inventory
- `documents` - Machine documentation

## 🔗 API Endpoints

### Machines
- `GET /api/machines` - List all machines
- `POST /api/machines` - Create new machine
- `GET /api/machines/:id` - Get machine details
- `PUT /api/machines/:id` - Update machine
- `DELETE /api/machines/:id` - Delete machine

### Files
- `GET /api/files/qr/:machineId` - Download QR code

## 🌐 Web Routes

- `/` - Redirect to admin dashboard
- `/admin` - Admin dashboard
- `/machines` - Machine list
- `/machines/new` - Add new machine form
- `/machines/:id` - Machine details
- `/operator/:machineNumber` - Operator view (mobile-friendly)

## 🚀 Deployment

### Production Setup

1. **Environment Configuration**
   ```bash
   NODE_ENV=production
   DB_PASSWORD=secure_password
   BASE_URL=https://your-domain.com
   ```

2. **Database Setup**
   - Create production database
   - Run migrations
   - Setup regular backups

3. **Security**
   - Use HTTPS
   - Set strong passwords
   - Configure firewall
   - Regular security updates

## 📝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Write tests for your changes
4. Implement the feature (following TDD)
5. Commit changes (`git commit -m 'Add amazing feature'`)
6. Push to branch (`git push origin feature/amazing-feature`)
7. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in `/docs`
- Review the test files for usage examples

## 🗺 Roadmap

- [x] Phase 1: Core machine management with QR codes
- [ ] Phase 2: Issue reporting and operator interface
- [ ] Phase 3: Maintenance planning and scheduling
- [ ] Phase 4: Spare parts management
- [ ] Phase 5: Document management
- [ ] Phase 6: Statistics and reporting
- [ ] Phase 7: Development projects tracking
