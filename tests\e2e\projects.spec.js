import { test, expect } from '@playwright/test';

// US-014: Arendusprojekti loomine
test('should create development project', async ({ page }) => {
  await page.goto('/projects');
  await page.click('[data-testid="new-project-btn"]');
  
  // Server-side rendered vorm
  await page.fill('[name="title"]', 'CNC tootlikkuse suurendamine');
  await page.fill('[name="description"]', 'Optimeerime lõikamise parameetreid');
  await page.fill('[name="initiator"]', 'Peeter Mets');
  await page.fill('[name="start_date"]', '2025-06-01');
  await page.fill('[name="end_date"]', '2025-12-31');
  
  // Masinate valimine (checkboxes)
  await page.check('[name="machines[]"][value="1"]'); // M-001
  await page.check('[name="machines[]"][value="2"]'); // M-002
  
  // Traditional form submit
  await page.click('[type="submit"]');
  
  // Redirect projektide nimekirja
  await expect(page).toHaveURL('/projects');
  await expect(page.locator('.alert-success')).toContainText('Projekt edukalt loodud');
  await expect(page.locator('tbody')).toContainText('CNC tootlikkuse suurendamine');
});

// US-015: Projekti progressi uuendamine
test('should update project progress', async ({ page }) => {
  await page.goto('/projects/1');
  await page.click('[data-testid="update-progress-btn"]');
  
  // AJAX vorm progressi uuendamiseks
  await page.fill('[name="progress_percentage"]', '75');
  await page.fill('[name="notes"]', 'Lõpetasime esimese faasi testimise');
  
  // AJAX submit (progressive enhancement)
  await page.click('[data-testid="save-progress"]');
  
  // Uuendus toimub ilma page reload'ita
  await expect(page.locator('.alert-success')).toContainText('Progress uuendatud');
  await expect(page.locator('[data-testid="progress-bar"]')).toHaveCSS('width', '75%');
  
  // Fallback: traditional form submit
});

// Test projects list page
test('should display projects list', async ({ page }) => {
  await page.goto('/projects');
  
  // Check page title and navigation
  await expect(page.locator('h1')).toContainText('Arendusprojektid');
  await expect(page.locator('[data-testid="projects-table"]')).toBeVisible();
  
  // Check navigation link is active
  await expect(page.locator('nav a[href="/projects"]')).toHaveClass(/bg-blue-800/);
});

// Test project detail view
test('should display project details', async ({ page }) => {
  await page.goto('/projects/1');
  
  // Check project information is displayed
  await expect(page.locator('[data-testid="project-title"]')).toBeVisible();
  await expect(page.locator('[data-testid="project-description"]')).toBeVisible();
  await expect(page.locator('[data-testid="progress-bar"]')).toBeVisible();
  await expect(page.locator('[data-testid="project-machines"]')).toBeVisible();
});
