import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { app } from '../../../server.js';

describe('Machines API', () => {
  beforeEach(async () => {
    // Clean up test data
    // This will be implemented when we have the database setup
  });

  afterEach(async () => {
    // Clean up after tests
  });

  describe('POST /api/machines', () => {
    it('should create a new machine', async () => {
      const timestamp = Date.now();
      const machineData = {
        machine_number: `M-TEST-${timestamp}`,
        name: 'Test Freespink',
        manufacturer: 'HAAS',
        model: 'VF-2',
        department: 'Tsehh A',
        location: 'Rida 1'
      };

      const response = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });

      expect(response.status).toBe(201);
      const result = await response.json();
      expect(result).toHaveProperty('id');
      expect(result.machine_number).toBe(`M-TEST-${timestamp}`);
      expect(result.name).toBe('Test Freespink');
    });

    it('should generate QR code when creating machine', async () => {
      const timestamp = Date.now() + 1;
      const machineData = {
        machine_number: `M-QR-${timestamp}`,
        name: 'Test Machine 2'
      };

      const response = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });

      expect(response.status).toBe(201);
      const result = await response.json();
      expect(result).toHaveProperty('qr_code_generated', true);
    });
  });

  describe('GET /api/machines/:id', () => {
    it('should get machine by id', async () => {
      // First create a machine
      const timestamp = Date.now() + 2;
      const machineData = {
        machine_number: `M-GET-${timestamp}`,
        name: 'Test Machine for GET'
      };

      const createResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });

      expect(createResponse.status).toBe(201);
      const createdMachine = await createResponse.json();

      // Now get the machine by id
      const response = await app.request(`/api/machines/${createdMachine.id}`);

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result).toHaveProperty('id', createdMachine.id);
      expect(result).toHaveProperty('machine_number', `M-GET-${timestamp}`);
      expect(result).toHaveProperty('name', 'Test Machine for GET');
    });
  });

  describe('PUT /api/machines/:id', () => {
    it('should update machine details', async () => {
      // First create a machine
      const timestamp = Date.now() + 3;
      const machineData = {
        machine_number: `M-PUT-${timestamp}`,
        name: 'Test Machine for PUT',
        location: 'Original Location'
      };

      const createResponse = await app.request('/api/machines', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(machineData)
      });

      expect(createResponse.status).toBe(201);
      const createdMachine = await createResponse.json();

      // Now update the machine
      const updateData = {
        name: 'Uuendatud Freespink',
        location: 'Tsehh B'
      };

      const response = await app.request(`/api/machines/${createdMachine.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result.name).toBe('Uuendatud Freespink');
      expect(result.location).toBe('Tsehh B');
      expect(result.machine_number).toBe(`M-PUT-${timestamp}`); // Should remain unchanged
    });
  });
});
